#!/usr/bin/env node

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const PROXY_URL = 'http://localhost:3003';
const API_KEY = 'kilo-claude-2025';

async function testCursorDevModel() {
    console.log('🧪 Testing cursordev model with tool support...\n');
    
    try {
        // Test 1: Check if cursordev model is available
        console.log('📋 Test 1: Checking available models...');
        const modelsResponse = await fetch(`${PROXY_URL}/v1/models`, {
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            }
        });

        if (!modelsResponse.ok) {
            throw new Error(`Models API error: ${modelsResponse.status} ${modelsResponse.statusText}`);
        }

        const modelsData = await modelsResponse.json();
        const cursordevModel = modelsData.data.find(model => model.id === 'cursordev');
        
        if (!cursordevModel) {
            console.log('❌ cursordev model not found in models list');
            return false;
        }

        console.log('✅ cursordev model found!');
        console.log(`   - ID: ${cursordevModel.id}`);
        console.log(`   - Function Calling: ${cursordevModel.capabilities?.function_calling}`);
        console.log(`   - Tools: ${cursordevModel.capabilities?.tools}`);
        console.log(`   - Vision: ${cursordevModel.capabilities?.vision}`);

        // Test 2: Test chat completion with tools
        console.log('\n💬 Test 2: Testing chat completion with tools...');
        const chatResponse = await fetch(`${PROXY_URL}/v1/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'cursordev',
                messages: [
                    {
                        role: 'user',
                        content: 'Hello! Can you help me with coding tasks? Please confirm you support tools.'
                    }
                ],
                tools: [
                    {
                        type: 'function',
                        function: {
                            name: 'get_current_time',
                            description: 'Get the current time',
                            parameters: {
                                type: 'object',
                                properties: {},
                                required: []
                            }
                        }
                    }
                ],
                max_tokens: 150
            })
        });

        if (!chatResponse.ok) {
            const errorText = await chatResponse.text();
            console.log(`❌ Chat completion error: ${chatResponse.status} ${chatResponse.statusText}`);
            console.log(`Error details: ${errorText}`);
            return false;
        }

        const chatData = await chatResponse.json();
        console.log('✅ Chat completion successful!');
        console.log(`🤖 Response: ${chatData.choices?.[0]?.message?.content || 'No content'}`);
        
        if (chatData.choices?.[0]?.message?.tool_calls) {
            console.log('🔧 Tool calls detected:', chatData.choices[0].message.tool_calls.length);
        }

        return true;

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        return false;
    }
}

async function main() {
    console.log('🚀 Starting cursordev model tests...\n');
    console.log(`📡 Proxy URL: ${PROXY_URL}`);
    console.log(`🔑 API Key: ${API_KEY}`);
    console.log('='.repeat(50));
    
    const success = await testCursorDevModel();
    
    console.log('\n' + '='.repeat(50));
    console.log('📋 TEST SUMMARY:');
    console.log(`✅ Overall Result: ${success ? 'PASSED' : 'FAILED'}`);
    
    if (success) {
        console.log('\n🎉 cursordev model is working with tool support!');
        console.log('💡 You can now use this model in Cursor with tools enabled.');
    } else {
        console.log('\n❌ cursordev model test failed.');
        console.log('💡 Make sure the proxy server is running: node cursor_proxy_server.cjs');
    }
}

// Add fetch polyfill for Node.js if needed
if (typeof fetch === 'undefined') {
    global.fetch = fetch;
}

main().catch(console.error);