{"policies": {"admin": {"allowed_actions": ["execute_command", "write_file", "read_file", "web_search", "analyze_code", "store_memory", "rbac_manage", "openwebui_*", "browser_*", "list_directory", "search_file_content", "glob", "web_fetch", "read_many_files", "fact_check", "research_topic"], "description": "Full system access including dangerous operations and RBAC management"}, "user": {"allowed_actions": ["read_file", "web_search", "analyze_code", "store_memory", "openwebui_web_search", "openwebui_get_models", "openwebui_get_conversations", "openwebui_create_chat", "list_directory", "search_file_content", "glob", "web_fetch", "read_many_files", "fact_check", "research_topic"], "description": "Safe operations only, no system modification"}, "guest": {"allowed_actions": ["web_search", "openwebui_web_search", "openwebui_get_models", "list_directory", "web_fetch", "fact_check", "research_topic"], "description": "Very limited read-only access"}}, "metadata": {"created": "2025-07-09T03:53:33.216Z", "version": "1.0.0", "description": "RBAC policies for Gemini CLI Wrapper Open WebUI integration"}}