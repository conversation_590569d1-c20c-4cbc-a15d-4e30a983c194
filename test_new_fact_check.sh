#!/bin/bash

# Test script for new fact_check tool using Gemini CLI

BASE_URL="http://localhost:8010"
API_KEY="test-key"

echo "🧪 Testing new fact_check tool with Gemini CLI..."

# Test 1: Basic fact check
echo -e "\n📋 Test 1: Basic fact check"
echo "Statement: 'Việt Nam có 63 tỉnh thành'"

curl -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "Please use the fact_check tool to verify this statement: Việt Nam có 63 tỉnh thành"
      }
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "fact_check",
          "description": "Fact-checks a statement or claim using Gemini CLI for accurate verification",
          "parameters": {
            "type": "object",
            "properties": {
              "statement": {
                "type": "string",
                "description": "The statement or claim to fact-check"
              },
              "detailed": {
                "type": "boolean",
                "description": "Whether to provide detailed analysis",
                "default": true
              }
            },
            "required": ["statement"]
          }
        }
      }
    ],
    "tool_choice": "auto"
  }' | jq '.'

echo -e "\n\n📋 Test 2: Research topic"
echo "Topic: 'Tình hình kinh tế Việt Nam 2024'"

curl -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {
        "role": "user",
        "content": "Please use the research_topic tool to research: Tình hình kinh tế Việt Nam 2024"
      }
    ],
    "tools": [
      {
        "type": "function",
        "function": {
          "name": "research_topic",
          "description": "Research a topic comprehensively using Gemini CLI",
          "parameters": {
            "type": "object",
            "properties": {
              "topic": {
                "type": "string",
                "description": "The topic to research"
              },
              "focus_areas": {
                "type": "array",
                "items": { "type": "string" },
                "description": "Specific areas to focus on in the research"
              },
              "depth": {
                "type": "string",
                "enum": ["basic", "detailed", "comprehensive"],
                "description": "Level of research depth",
                "default": "detailed"
              }
            },
            "required": ["topic"]
          }
        }
      }
    ],
    "tool_choice": "auto"
  }' | jq '.'

echo -e "\n✅ Test completed!"
