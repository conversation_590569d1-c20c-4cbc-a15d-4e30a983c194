#!/bin/bash

# Script khởi động nhanh tất cả services
# Tối ưu hóa cho việc khởi động và kiểm tra nhanh

# M<PERSON>u sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Quick Start All Services ===${NC}"

# Chuyển đến thư mục dự án
cd "/home/<USER>/Gemini CLI Wrapper"

# Kiểm tra PM2 có đang chạy không
if ! pgrep -f "PM2" > /dev/null; then
    echo -e "${YELLOW}PM2 chưa chạy, đang khởi động...${NC}"
    pm2 resurrect
fi

# Kiểm tra trạng thái hiện tại
echo -e "${BLUE}Trạng thái hiện tại:${NC}"
pm2 status

# Kiểm tra xem các services có đang chạy không
GEMINI_STATUS=$(pm2 jlist | jq -r '.[] | select(.name=="gemini-cli-wrapper") | .pm2_env.status' 2>/dev/null || echo "stopped")
MCP_STATUS=$(pm2 jlist | jq -r '.[] | select(.name=="mcp-search-engine") | .pm2_env.status' 2>/dev/null || echo "stopped")

echo -e "\n${BLUE}Service Status:${NC}"
echo -e "Gemini CLI Wrapper: $GEMINI_STATUS"
echo -e "MCP Search Engine: $MCP_STATUS"

# Khởi động lại nếu cần
if [[ "$GEMINI_STATUS" != "online" || "$MCP_STATUS" != "online" ]]; then
    echo -e "\n${YELLOW}Khởi động lại services...${NC}"
    
    # Restart services
    pm2 restart ecosystem.config.cjs
    
    # Đợi một chút để services khởi động
    echo -e "${YELLOW}Đợi services khởi động...${NC}"
    sleep 5
    
    echo -e "${GREEN}✅ Services đã được khởi động lại${NC}"
else
    echo -e "${GREEN}✅ Tất cả services đang chạy bình thường${NC}"
fi

# Hiển thị trạng thái cuối cùng
echo -e "\n${BLUE}Trạng thái cuối cùng:${NC}"
pm2 status

# Quick test
echo -e "\n${BLUE}Quick Health Check:${NC}"

# Test Gemini CLI Wrapper
GEMINI_HEALTH=$(curl -s http://localhost:8010/health --max-time 5 | jq -r '.status' 2>/dev/null || echo "failed")
if [[ "$GEMINI_HEALTH" == "healthy" ]]; then
    echo -e "${GREEN}✅ Gemini CLI Wrapper: Healthy${NC}"
else
    echo -e "${RED}❌ Gemini CLI Wrapper: $GEMINI_HEALTH${NC}"
fi

# Test MCP Search Engine (simple connectivity test)
MCP_HEALTH=$(curl -s http://localhost:7860 --max-time 3 -o /dev/null -w "%{http_code}" || echo "000")
if [[ "$MCP_HEALTH" == "404" || "$MCP_HEALTH" == "200" ]]; then
    echo -e "${GREEN}✅ MCP Search Engine: Responding${NC}"
else
    echo -e "${RED}❌ MCP Search Engine: Not responding (HTTP $MCP_HEALTH)${NC}"
fi

# Hiển thị thông tin truy cập
SERVER_IP=$(hostname -I | awk '{print $1}')
echo -e "\n${GREEN}🚀 Services Ready!${NC}"
echo -e "${BLUE}Gemini CLI Wrapper:${NC} http://localhost:8010"
echo -e "${BLUE}MCP Search Engine:${NC} http://localhost:7860"
echo -e "${BLUE}External IP:${NC} $SERVER_IP"

echo -e "\n${YELLOW}💡 Tip: Sử dụng './test_all_services.sh' để test đầy đủ${NC}"
