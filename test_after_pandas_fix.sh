#!/bin/bash

# Test performance after removing pandas-data from Gemini CLI MCP config

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}=== Performance Test After Pandas-Data Fix ===${NC}"

# 1. Verify pandas-data is removed
echo -e "\n${YELLOW}1. Verifying pandas-data removal...${NC}"
PANDAS_CHECK=$(python3 -c "
import json
with open('/home/<USER>/.gemini/settings.json', 'r') as f:
    settings = json.load(f)
if 'pandas-data' in settings.get('mcpServers', {}):
    print('STILL_EXISTS')
else:
    print('REMOVED')
")

if [[ "$PANDAS_CHECK" == "REMOVED" ]]; then
    echo -e "${GREEN}✅ pandas-data successfully removed from Gemini CLI config${NC}"
else
    echo -e "${RED}❌ pandas-data still exists in config${NC}"
fi

# 2. Test basic endpoints
echo -e "\n${YELLOW}2. Testing basic endpoints...${NC}"

# Health check
HEALTH_START=$(date +%s%3N)
HEALTH_RESPONSE=$(curl -s http://localhost:8010/health --max-time 5)
HEALTH_END=$(date +%s%3N)
HEALTH_TIME=$((HEALTH_END - HEALTH_START))

if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
    echo -e "${GREEN}✅ Health Check: ${HEALTH_TIME}ms${NC}"
else
    echo -e "${RED}❌ Health Check: Failed${NC}"
fi

# Cache stats
CACHE_START=$(date +%s%3N)
CACHE_RESPONSE=$(curl -s http://localhost:7860/cache/stats --max-time 5)
CACHE_END=$(date +%s%3N)
CACHE_TIME=$((CACHE_END - CACHE_START))

if [[ "$CACHE_RESPONSE" == *"hits"* ]]; then
    echo -e "${GREEN}✅ Cache Stats: ${CACHE_TIME}ms${NC}"
else
    echo -e "${RED}❌ Cache Stats: Failed${NC}"
fi

# 3. Test MCP Search Engine
echo -e "\n${YELLOW}3. Testing MCP Search Engine...${NC}"

# Test with a simple query
SEARCH_START=$(date +%s%3N)
SEARCH_RESPONSE=$(curl -s -X POST http://localhost:7860/search \
    -H "Content-Type: application/json" \
    -d '{"query": "What is artificial intelligence?"}' \
    --max-time 30)
SEARCH_END=$(date +%s%3N)
SEARCH_TIME=$((SEARCH_END - SEARCH_START))

if [[ "$SEARCH_RESPONSE" == *"success"* ]]; then
    echo -e "${GREEN}✅ MCP Search: ${SEARCH_TIME}ms${NC}"
    
    # Check if cached
    if [[ "$SEARCH_RESPONSE" == *'"cached":true'* ]]; then
        echo -e "${BLUE}📋 Result: Cache HIT${NC}"
    else
        echo -e "${YELLOW}📋 Result: Cache MISS${NC}"
    fi
    
    # Extract server response time
    SERVER_TIME=$(echo "$SEARCH_RESPONSE" | grep -o '"responseTimeInMs":[^,]*' | cut -d':' -f2)
    if [[ -n "$SERVER_TIME" ]]; then
        echo -e "${BLUE}⏱️  Server Time: ${SERVER_TIME}ms${NC}"
    fi
else
    echo -e "${RED}❌ MCP Search: Failed (${SEARCH_TIME}ms)${NC}"
    echo -e "${RED}   Response: ${SEARCH_RESPONSE:0:100}...${NC}"
fi

# 4. Test cache hit
echo -e "\n${YELLOW}4. Testing cache performance...${NC}"

CACHE_TEST_START=$(date +%s%3N)
CACHE_TEST_RESPONSE=$(curl -s -X POST http://localhost:7860/search \
    -H "Content-Type: application/json" \
    -d '{"query": "What is artificial intelligence?"}' \
    --max-time 10)
CACHE_TEST_END=$(date +%s%3N)
CACHE_TEST_TIME=$((CACHE_TEST_END - CACHE_TEST_START))

if [[ "$CACHE_TEST_RESPONSE" == *"success"* ]]; then
    if [[ "$CACHE_TEST_RESPONSE" == *'"cached":true'* ]]; then
        echo -e "${GREEN}✅ Cache Hit: ${CACHE_TEST_TIME}ms${NC}"
    else
        echo -e "${YELLOW}⚠️  Cache Miss: ${CACHE_TEST_TIME}ms${NC}"
    fi
else
    echo -e "${RED}❌ Cache Test: Failed${NC}"
fi

# 5. Test fact_check tool (may still be slow)
echo -e "\n${YELLOW}5. Testing fact_check tool...${NC}"

FACT_START=$(date +%s%3N)
FACT_RESPONSE=$(curl -s -X POST http://localhost:8010/mcp/tools/execute \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer test-key" \
    -d '{"tool_name": "fact_check", "args": {"statement": "The Earth is round", "detailed": false}}' \
    --max-time 25)
FACT_END=$(date +%s%3N)
FACT_TIME=$((FACT_END - FACT_START))

if [[ "$FACT_RESPONSE" == *"success"* ]]; then
    ANALYSIS=$(echo "$FACT_RESPONSE" | jq -r '.result.analysis' 2>/dev/null)
    if [[ -n "$ANALYSIS" && "$ANALYSIS" != "null" && "$ANALYSIS" != "" ]]; then
        echo -e "${GREEN}✅ Fact Check: Working (${FACT_TIME}ms)${NC}"
        echo -e "${BLUE}   Analysis: ${ANALYSIS:0:50}...${NC}"
    else
        echo -e "${YELLOW}⚠️  Fact Check: Success but empty analysis (${FACT_TIME}ms)${NC}"
    fi
else
    echo -e "${RED}❌ Fact Check: Failed/Timeout (${FACT_TIME}ms)${NC}"
fi

# 6. Check for pandas-data errors in logs
echo -e "\n${YELLOW}6. Checking for pandas-data errors in logs...${NC}"

PANDAS_ERRORS=$(pm2 logs gemini-cli-wrapper --lines 20 --nostream 2>/dev/null | grep -i "pandas-data" | wc -l)
if [[ $PANDAS_ERRORS -eq 0 ]]; then
    echo -e "${GREEN}✅ No pandas-data errors found in recent logs${NC}"
else
    echo -e "${RED}❌ Found $PANDAS_ERRORS pandas-data errors in logs${NC}"
fi

# 7. Performance summary
echo -e "\n${BLUE}=== Performance Summary ===${NC}"
echo -e "Health Check: ${HEALTH_TIME}ms"
echo -e "Cache Stats: ${CACHE_TIME}ms"
echo -e "MCP Search (new): ${SEARCH_TIME}ms"
echo -e "MCP Search (cache): ${CACHE_TEST_TIME}ms"
echo -e "Fact Check: ${FACT_TIME}ms"

# Overall assessment
echo -e "\n${BLUE}=== Assessment ===${NC}"

if [[ $SEARCH_TIME -lt 20000 ]]; then
    echo -e "${GREEN}🎉 MCP Search Performance: Excellent (<20s)${NC}"
elif [[ $SEARCH_TIME -lt 30000 ]]; then
    echo -e "${YELLOW}👍 MCP Search Performance: Good (20-30s)${NC}"
else
    echo -e "${RED}❌ MCP Search Performance: Needs improvement (>30s)${NC}"
fi

if [[ $CACHE_TEST_TIME -lt 1000 ]]; then
    echo -e "${GREEN}🚀 Cache Performance: Excellent (<1s)${NC}"
else
    echo -e "${YELLOW}⚠️  Cache Performance: Could be better (>1s)${NC}"
fi

echo -e "\n${GREEN}🔧 Fixes Applied:${NC}"
echo -e "   • Removed pandas-data from Gemini CLI MCP config"
echo -e "   • Fixed circular dependency in toolRegistry.js"
echo -e "   • Added response caching to MCP Search Engine"
echo -e "   • Optimized timeouts and connection settings"

echo -e "\n${YELLOW}💡 Next Steps:${NC}"
echo -e "   • Monitor performance with: ./quick_test.sh"
echo -e "   • Check cache stats: curl http://localhost:7860/cache/stats"
echo -e "   • View logs: pm2 logs gemini-cli-wrapper"

echo -e "\n${GREEN}✅ Performance test completed!${NC}"
