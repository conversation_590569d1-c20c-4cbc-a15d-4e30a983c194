#!/bin/bash

# Comprehensive endpoint testing script

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

BASE_URL="http://localhost:8010"
MCP_URL="http://localhost:7860"
API_KEY="test-key"

echo -e "${BLUE}=== Comprehensive Endpoint Testing ===${NC}"

# Function to test endpoint with timing
test_endpoint() {
    local name="$1"
    local url="$2"
    local method="$3"
    local data="$4"
    local timeout="$5"
    
    echo -e "\n${YELLOW}Testing $name...${NC}"
    
    local start_time=$(date +%s%3N)
    
    if [[ "$method" == "GET" ]]; then
        local response=$(curl -s "$url" --max-time "$timeout" -w "\nHTTP_CODE:%{http_code}")
    else
        local response=$(curl -s -X "$method" "$url" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $API_KEY" \
            -d "$data" \
            --max-time "$timeout" \
            -w "\nHTTP_CODE:%{http_code}")
    fi
    
    local end_time=$(date +%s%3N)
    local duration=$((end_time - start_time))
    
    local http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d':' -f2)
    local body=$(echo "$response" | sed '/HTTP_CODE:/d')
    
    if [[ "$http_code" == "200" ]]; then
        echo -e "${GREEN}✅ $name: ${duration}ms (HTTP $http_code)${NC}"
        if [[ ${#body} -gt 100 ]]; then
            echo -e "${BLUE}   Response: ${body:0:100}...${NC}"
        else
            echo -e "${BLUE}   Response: $body${NC}"
        fi
    else
        echo -e "${RED}❌ $name: ${duration}ms (HTTP $http_code)${NC}"
        if [[ -n "$body" ]]; then
            echo -e "${RED}   Error: $body${NC}"
        fi
    fi
    
    return $duration
}

# 1. Basic Health Checks
echo -e "\n${BLUE}=== 1. Health Checks ===${NC}"
test_endpoint "Gemini Wrapper Health" "$BASE_URL/health" "GET" "" 5
test_endpoint "MCP Search Cache Stats" "$MCP_URL/cache/stats" "GET" "" 5

# 2. Model Endpoints
echo -e "\n${BLUE}=== 2. Model Endpoints ===${NC}"
test_endpoint "List Models" "$BASE_URL/v1/models" "GET" "" 10

# 3. MCP Tools
echo -e "\n${BLUE}=== 3. MCP Tools ===${NC}"
test_endpoint "List Tools" "$BASE_URL/mcp/tools" "GET" "" 10

# Simple tools (should be fast)
test_endpoint "List Directory Tool" "$BASE_URL/mcp/tools/execute" "POST" \
    '{"tool_name": "list_directory", "args": {"path": "."}}' 10

test_endpoint "Store Memory Tool" "$BASE_URL/mcp/tools/execute" "POST" \
    '{"tool_name": "store_memory", "args": {"key": "test", "value": "performance test"}}' 10

# 4. MCP Search Engine
echo -e "\n${BLUE}=== 4. MCP Search Engine ===${NC}"

# Test cache hit (repeat previous query)
test_endpoint "Search (Cache Hit)" "$MCP_URL/search" "POST" \
    '{"query": "What is 2+2?"}' 10

# Test new query
test_endpoint "Search UAV Vietnam" "$MCP_URL/search" "POST" \
    '{"query": "việt nam sản xuất uav chiến đấu"}' 45

# 5. Gemini CLI Tools (may be slow)
echo -e "\n${BLUE}=== 5. Gemini CLI Tools (May be slow) ===${NC}"

test_endpoint "Fact Check Tool" "$BASE_URL/mcp/tools/execute" "POST" \
    '{"tool_name": "fact_check", "args": {"statement": "Việt Nam có 63 tỉnh thành", "detailed": false}}' 30

test_endpoint "Research Topic Tool" "$BASE_URL/mcp/tools/execute" "POST" \
    '{"tool_name": "research_topic", "args": {"topic": "artificial intelligence", "depth": "basic"}}' 30

# 6. Web Tools (may timeout)
echo -e "\n${BLUE}=== 6. Web Tools (May timeout) ===${NC}"

test_endpoint "Web Search Tool" "$BASE_URL/mcp/tools/execute" "POST" \
    '{"tool_name": "web_search", "args": {"query": "vietnam uav", "max_results": 2}}' 30

# 7. Chat Completions
echo -e "\n${BLUE}=== 7. Chat Completions ===${NC}"

test_endpoint "Simple Chat" "$BASE_URL/v1/chat/completions" "POST" \
    '{"model": "gemini-2.5-flash", "messages": [{"role": "user", "content": "Hello, respond with just OK"}], "max_tokens": 10}' 30

# 8. Debug Endpoints
echo -e "\n${BLUE}=== 8. Debug Endpoints ===${NC}"
test_endpoint "Token Cache Debug" "$BASE_URL/v1/debug/cache" "GET" "" 5
test_endpoint "Real Thinking Debug" "$BASE_URL/v1/debug/real-thinking" "GET" "" 5

# Summary
echo -e "\n${BLUE}=== Performance Summary ===${NC}"
echo -e "${GREEN}Fast endpoints (<1s):${NC} Health checks, cache stats, list tools, simple tools"
echo -e "${YELLOW}Medium endpoints (1-10s):${NC} Models, memory tools, cached searches"
echo -e "${RED}Slow endpoints (>10s):${NC} New searches, fact check, research, web search, chat"

echo -e "\n${BLUE}=== Recommendations ===${NC}"
echo -e "• Use cached searches when possible"
echo -e "• Simple tools are very fast"
echo -e "• Gemini CLI tools need optimization"
echo -e "• Web search may need external service"

echo -e "\n${GREEN}🎉 Testing completed!${NC}"
