#!/bin/bash

# Performance Benchmark Script
# Measures response times before and after optimizations

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Performance Benchmark Test ===${NC}"

# Test queries
QUERIES=(
    "What is JavaScript?"
    "Explain Docker containers"
    "How does React work?"
    "What is machine learning?"
    "Explain REST APIs"
)

# Function to test MCP Search Engine performance
test_mcp_performance() {
    local query="$1"
    local test_num="$2"
    
    echo -e "\n${YELLOW}Test $test_num: \"$query\"${NC}"
    
    local start_time=$(date +%s%3N)
    local response=$(curl -s -X POST http://localhost:7860/search \
        -H "Content-Type: application/json" \
        -d "{\"query\": \"$query\"}" \
        --max-time 60)
    local end_time=$(date +%s%3N)
    
    local response_time=$((end_time - start_time))
    
    if [[ "$response" == *"success"* ]]; then
        local cached=$(echo "$response" | grep -o '"cached":[^,]*' | cut -d':' -f2)
        local server_time=$(echo "$response" | grep -o '"responseTimeInMs":[^,]*' | cut -d':' -f2)
        
        echo -e "${GREEN}✅ Success: ${response_time}ms total, ${server_time}ms server${NC}"
        if [[ "$cached" == "true" ]]; then
            echo -e "${BLUE}📋 Cache HIT${NC}"
        else
            echo -e "${YELLOW}📋 Cache MISS${NC}"
        fi
        
        echo "$response_time" # Return response time for averaging
    else
        echo -e "${RED}❌ Failed: ${response_time}ms${NC}"
        echo "0" # Return 0 for failed requests
    fi
}

# Function to run benchmark
run_benchmark() {
    echo -e "\n${BLUE}🚀 Starting benchmark...${NC}"
    
    local total_time=0
    local successful_tests=0
    local cache_hits=0
    
    # Run tests
    for i in "${!QUERIES[@]}"; do
        local query="${QUERIES[$i]}"
        local test_num=$((i + 1))
        
        local response_time=$(test_mcp_performance "$query" "$test_num")
        
        if [[ "$response_time" -gt 0 ]]; then
            total_time=$((total_time + response_time))
            successful_tests=$((successful_tests + 1))
        fi
        
        # Small delay between tests
        sleep 1
    done
    
    # Test cache hits by repeating first query
    echo -e "\n${BLUE}🔄 Testing cache performance (repeat first query)...${NC}"
    local cache_test_time=$(test_mcp_performance "${QUERIES[0]}" "Cache Test")
    
    # Calculate averages
    if [[ $successful_tests -gt 0 ]]; then
        local avg_time=$((total_time / successful_tests))
        echo -e "\n${GREEN}📊 Benchmark Results:${NC}"
        echo -e "   • Total tests: ${#QUERIES[@]}"
        echo -e "   • Successful: $successful_tests"
        echo -e "   • Average response time: ${avg_time}ms"
        echo -e "   • Cache test time: ${cache_test_time}ms"
        
        # Performance rating
        if [[ $avg_time -lt 10000 ]]; then
            echo -e "   • Performance: ${GREEN}Excellent (<10s)${NC}"
        elif [[ $avg_time -lt 20000 ]]; then
            echo -e "   • Performance: ${YELLOW}Good (10-20s)${NC}"
        elif [[ $avg_time -lt 40000 ]]; then
            echo -e "   • Performance: ${YELLOW}Fair (20-40s)${NC}"
        else
            echo -e "   • Performance: ${RED}Poor (>40s)${NC}"
        fi
    else
        echo -e "\n${RED}❌ All tests failed${NC}"
    fi
}

# Function to show cache statistics
show_cache_stats() {
    echo -e "\n${BLUE}📊 Cache Statistics:${NC}"
    local cache_stats=$(curl -s http://localhost:7860/cache/stats)
    
    if [[ "$cache_stats" == *"hits"* ]]; then
        echo "$cache_stats" | jq '.' 2>/dev/null || echo "$cache_stats"
    else
        echo -e "${RED}❌ Could not retrieve cache stats${NC}"
    fi
}

# Function to show PM2 status
show_pm2_status() {
    echo -e "\n${BLUE}🔧 PM2 Status:${NC}"
    pm2 list
}

# Function to run continuous monitoring
continuous_monitor() {
    echo -e "\n${YELLOW}🔄 Starting continuous monitoring (Ctrl+C to stop)...${NC}"
    
    while true; do
        echo -e "\n${BLUE}$(date): Testing...${NC}"
        test_mcp_performance "Quick test $(date +%s)" "Monitor"
        sleep 10
    done
}

# Main menu
show_menu() {
    echo -e "\n${BLUE}Select benchmark option:${NC}"
    echo "1. Run full benchmark"
    echo "2. Show cache statistics"
    echo "3. Show PM2 status"
    echo "4. Continuous monitoring"
    echo "5. Quick single test"
    echo "6. Exit"
    echo -n "Enter choice [1-6]: "
}

# Function for quick single test
quick_test() {
    echo -e "\n${YELLOW}Enter test query:${NC}"
    read -r query
    test_mcp_performance "$query" "Quick Test"
}

# Main execution
main() {
    # Check if services are running
    echo -e "${YELLOW}🔍 Checking services...${NC}"
    
    local gemini_health=$(curl -s http://localhost:8010/health --max-time 5)
    if [[ "$gemini_health" == *"healthy"* ]]; then
        echo -e "${GREEN}✅ Gemini Wrapper: Running${NC}"
    else
        echo -e "${RED}❌ Gemini Wrapper: Not responding${NC}"
        echo -e "${YELLOW}💡 Try: pm2 restart gemini-cli-wrapper${NC}"
    fi
    
    local mcp_test=$(curl -s http://localhost:7860/cache/stats --max-time 5)
    if [[ "$mcp_test" == *"hits"* ]]; then
        echo -e "${GREEN}✅ MCP Search Engine: Running${NC}"
    else
        echo -e "${RED}❌ MCP Search Engine: Not responding${NC}"
        echo -e "${YELLOW}💡 Try: pm2 restart mcp-search-engine${NC}"
    fi
    
    # Interactive menu
    while true; do
        show_menu
        read -r choice
        
        case $choice in
            1)
                run_benchmark
                ;;
            2)
                show_cache_stats
                ;;
            3)
                show_pm2_status
                ;;
            4)
                continuous_monitor
                ;;
            5)
                quick_test
                ;;
            6)
                echo -e "${GREEN}👋 Goodbye!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Invalid option${NC}"
                ;;
        esac
    done
}

# Check if jq is available for JSON formatting
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}💡 Install 'jq' for better JSON formatting: sudo apt install jq${NC}"
fi

# Run main function
main
