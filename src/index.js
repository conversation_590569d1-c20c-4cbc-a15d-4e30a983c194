import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import os from 'os';
import { providerManager } from './providers/providerManager.js';
import { authMiddleware } from './middleware/auth.js';
import { errorHandler } from './middleware/errorHandler.js';
import { logger } from './utils/logger.js';
import { mcpHandler } from './mcp/mcpHandler.js';
import { toolRegistry } from './tools/toolRegistry.js';
import { rbacManager, requirePermission } from './middleware/rbac.js';
import difyRoutes from './dify/difyRoutes.js';
 
dotenv.config();

const app = express();
const PORT = process.env.PORT || 8010;

// Enhanced CORS configuration
app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    // Allow localhost and VPS IP origins
    const allowedOrigins = [
      'http://localhost:3000',
      'http://127.0.0.1:3000',
      'http://localhost:8080',
      'http://127.0.0.1:8080',
      'http://localhost:3001',
      'http://127.0.0.1:3001',
      'http://**********:3000',
      'http://**********:8080',
      'http://**********:3001',
      'http://**************:3000',
      'http://**************:8080',
      'http://**************:3001'
    ];
    
    // Allow any localhost port for development
    if (origin.match(/^https?:\/\/(localhost|127\.0\.0\.1):\d+$/)) {
      return callback(null, true);
    }
    
    // Allow internal VPS IP with any port
    if (origin.match(/^https?:\/\/10\.0\.0\.153:\d+$/)) {
      return callback(null, true);
    }
    
    // Allow external VPS IP with any port
    if (origin.match(/^https?:\/\/217\.142\.186\.49:\d+$/)) {
      return callback(null, true);
    }
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      // For production, you might want to be more restrictive
      // For now, allow all origins for maximum compatibility
      callback(null, true);
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'HEAD', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  exposedHeaders: ['Content-Length', 'Content-Type'],
  credentials: false, // Changed to false for better compatibility
  maxAge: 86400 // 24 hours
}));

// Debug middleware to log CORS requests
app.use((req, res, next) => {
  if (req.method === 'OPTIONS' || req.headers.origin) {
    console.log(`[CORS] ${req.method} ${req.path} from origin: ${req.headers.origin || 'no-origin'}`);
    console.log(`[CORS] Headers:`, req.headers);
  }
  next();
});

// Handle preflight requests explicitly
app.options('*', (req, res) => {
  console.log(`[CORS] Preflight request for ${req.path} from ${req.headers.origin || 'no-origin'}`);
  res.status(200).end();
});

app.use(express.json());
app.use(express.static('public'));
app.use((req, res, next) => logger.request(req, res, next));

// Initialize Provider Manager (handles both Gemini and Claude)
// Note: providerManager is already initialized as singleton

// Public routes
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Modified authMiddleware with configurable OpenAI API security
const modifiedAuthMiddleware = (req, res, next) => {
  // Always skip auth for health check
  if (req.path === '/health') {
    return next();
  }
  
  // Check if OpenAI API endpoints should require authentication
  const requireOpenAIAuth = process.env.REQUIRE_OPENAI_AUTH === 'true';
  
  if (req.path.startsWith('/v1/') && !requireOpenAIAuth) {
    // Skip auth for OpenAI API endpoints if not required
    logger.info('🔓 OpenAI API endpoint accessed without authentication (REQUIRE_OPENAI_AUTH=false)');
    return next();
  }
  
  // For OpenAI API endpoints when auth is required, check for dedicated OpenWebUI API key
  if (req.path.startsWith('/v1/') && requireOpenAIAuth) {
    const authHeader = req.headers.authorization;
    const openWebUIKey = process.env.OPENWEBUI_API_KEY;
    
    if (!authHeader) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'API key required for OpenAI endpoints'
      });
    }
    
    // Extract token from "Bearer <token>" format
    const token = authHeader.startsWith('Bearer ')
      ? authHeader.substring(7)
      : authHeader;
    
    // Check if it matches the dedicated OpenWebUI API key
    if (openWebUIKey && token === openWebUIKey) {
      logger.info('✅ OpenWebUI API key authentication successful');
      // Set a default role for OpenWebUI access
      req.userRole = 'user';
      return next();
    }
    
    // If no dedicated key or doesn't match, fall back to regular auth
    logger.info('🔐 Falling back to regular authentication for /v1/ endpoint');
  }
  
  // Use the original authMiddleware for all other routes (including /v1/ if auth is required)
  authMiddleware(req, res, next);
};

// Protected routes
const protectedRouter = express.Router();
protectedRouter.use(modifiedAuthMiddleware);

// OpenAI compatible routes
protectedRouter.get('/v1/models', async (req, res) => {
  try {
    const models = await providerManager.getAvailableModels();
    // Transform to OpenAI format with enhanced capabilities
    const openAIModels = models.map(model => ({
      id: model.id,
      object: "model",
      created: Date.now(),
      owned_by: "gemini-cli-wrapper",
      permission: [],
      root: model.id,
      parent: null,
      capabilities: {
        vision: model.vision || false,
        thinking: model.thinking || false,
        max_tokens: model.maxTokens || 1000000
      }
    }));
    res.json({ data: openAIModels, object: "list" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.post('/v1/chat/completions', async (req, res) => {
  try {
    const {
      model,
      messages,
      stream = false,
      tools,
      tool_choice,
      reasoning_effort,
      thinking_budget,
      include_reasoning,
      extra_body,
      ...options
    } = req.body;
    
    // Add model to options
    if (model) {
      options.model = model;
    }
    
    // Add tools and session to options if provided
    if (tools) {
      options.tools = tools;
      options.tool_choice = tool_choice;
    }

    // Extract session_id from various sources
    const sessionId = req.body.session_id ||
                     req.headers['x-session-id'] ||
                     req.headers['x-chat-id'] ||
                     process.env.OPENWEBUI_CURRENT_SESSION_ID ||
                     'default';

    if (sessionId) {
      options.session_id = sessionId;
    }

    // Add reasoning/thinking parameters
    if (reasoning_effort !== undefined) {
      options.reasoning_effort = reasoning_effort;
    }
    
    if (thinking_budget !== undefined) {
      options.thinking_budget = thinking_budget;
    }
    
    if (include_reasoning !== undefined) {
      options.include_reasoning = include_reasoning;
    }
    
    // Handle extra_body parameters (Open WebUI compatibility)
    if (extra_body && typeof extra_body === 'object') {
      if (extra_body.reasoning_effort !== undefined) {
        options.reasoning_effort = extra_body.reasoning_effort;
      }
      if (extra_body.thinking_budget !== undefined) {
        options.thinking_budget = extra_body.thinking_budget;
      }
      if (extra_body.include_reasoning !== undefined) {
        options.include_reasoning = extra_body.include_reasoning;
      }
    }
    
    if (stream) {
      // Handle streaming response
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      
      await providerManager.streamCompletion(messages, options, (chunk) => {
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      });
      
      res.write('data: [DONE]\n\n');
      res.end();
    } else {
      // Handle regular response
      const response = await providerManager.createCompletion(messages, options);
      res.json(response);
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Original routes
protectedRouter.get('/models', async (req, res) => {
  try {
    const models = await providerManager.getAvailableModels();
    res.json({ models });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete all models (reset to defaults)
protectedRouter.delete('/models', async (req, res) => {
  try {
    await providerManager.resetModels();
    res.json({ success: true, message: 'All models reset to defaults' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Add custom model
protectedRouter.post('/models', async (req, res) => {
  try {
    const { id, name, description, maxTokens } = req.body;
    
    if (!id || !name) {
      return res.status(400).json({ error: 'Model ID and name are required' });
    }
    
    const result = await providerManager.addCustomModel({
      id, name, description, maxTokens: maxTokens || 1000000
    });
    
    res.json({ success: true, model: result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Delete specific model
protectedRouter.delete('/models/:modelId', async (req, res) => {
  try {
    const { modelId } = req.params;
    const result = await providerManager.deleteModel(modelId);
    
    if (result) {
      res.json({ success: true, message: `Model ${modelId} deleted` });
    } else {
      res.status(404).json({ error: `Model ${modelId} not found` });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.get('/status', async (req, res) => {
  try {
    const status = await providerManager.getStatus();
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// MCP endpoints
protectedRouter.get('/mcp/info', (req, res) => {
  res.json(mcpHandler.getServerInfo());
});

protectedRouter.get('/mcp/tools', (req, res) => {
  res.json({
    tools: mcpHandler.formatToolsForAPI()
  });
});

protectedRouter.get('/mcp/sessions/:sessionId?', (req, res) => {
  const sessionId = req.params.sessionId;
  if (sessionId) {
    const context = mcpHandler.getSessionContext(sessionId);
    if (!context) {
      return res.status(404).json({ error: 'Session not found' });
    }
    res.json(context);
  } else {
    // List all sessions
    const sessions = [];
    for (const [id] of mcpHandler.sessions.entries()) {
      sessions.push(mcpHandler.getSessionContext(id));
    }
    res.json({ sessions });
  }
});

protectedRouter.post('/mcp/tools/execute', async (req, res) => {
  try {
    const { tool_name, args, arguments: argumentsAlias, session_id } = req.body;
    
    if (!tool_name) {
      return res.status(400).json({ error: 'tool_name is required' });
    }
    
    // Support both 'args' and 'arguments' for compatibility
    const toolArgs = args || argumentsAlias || {};
    
    // Create user context for RBAC
    const userContext = {
      userRole: req.userRole || 'user',
      ip: req.ip || req.connection.remoteAddress,
      userAgent: req.headers['user-agent'],
      path: req.path,
      method: req.method
    };
    
    const result = await toolRegistry.executeTool(tool_name, toolArgs, userContext);
    
    // Store in session if provided
    if (session_id) {
      const session = mcpHandler.initializeSession(session_id);
      session.context.set(`tool_${tool_name}_last`, result);
    }
    
    res.json({
      success: true,
      tool: tool_name,
      result,
      session_id,
      user_role: userContext.userRole
    });
  } catch (error) {
    // Check if this is an RBAC authorization error
    if (error.message.includes('Access denied')) {
      return res.status(403).json({
        success: false,
        error: 'Access denied',
        message: error.message,
        tool: req.body.tool_name,
        user_role: req.userRole || 'user'
      });
    }
    
    res.status(500).json({
      success: false,
      error: error.message,
      tool: req.body.tool_name
    });
  }
});

protectedRouter.delete('/mcp/sessions/:sessionId', (req, res) => {
  const sessionId = req.params.sessionId;
  const deleted = mcpHandler.sessions.delete(sessionId);
  
  res.json({
    success: deleted,
    message: deleted ? 'Session deleted' : 'Session not found'
  });
});

protectedRouter.post('/chat/completions', async (req, res) => {
  try {
    const {
      model,
      messages,
      stream = false,
      tools,
      tool_choice,
      session_id,
      reasoning_effort,
      thinking_budget,
      include_reasoning,
      extra_body,
      ...options
    } = req.body;
    
    // Add model to options
    if (model) {
      options.model = model;
    }
    
    // Add tools and session to options
    if (tools) {
      options.tools = tools;
      options.tool_choice = tool_choice;
    }
    
    if (session_id) {
      options.session_id = session_id;
    }
    
    // Add reasoning/thinking parameters
    if (reasoning_effort !== undefined) {
      options.reasoning_effort = reasoning_effort;
    }
    
    if (thinking_budget !== undefined) {
      options.thinking_budget = thinking_budget;
    }
    
    if (include_reasoning !== undefined) {
      options.include_reasoning = include_reasoning;
    }
    
    // Handle extra_body parameters (Open WebUI compatibility)
    if (extra_body && typeof extra_body === 'object') {
      if (extra_body.reasoning_effort !== undefined) {
        options.reasoning_effort = extra_body.reasoning_effort;
      }
      if (extra_body.thinking_budget !== undefined) {
        options.thinking_budget = extra_body.thinking_budget;
      }
      if (extra_body.include_reasoning !== undefined) {
        options.include_reasoning = extra_body.include_reasoning;
      }
    }
    
    // Clean up old sessions periodically
    if (Math.random() < 0.1) { // 10% chance
      mcpHandler.cleanupSessions();

      // Also cleanup cursor provider sessions
      const cursorProvider = providerManager.providers.get('cursor');
      if (cursorProvider && cursorProvider.cleanupInactiveSessions) {
        cursorProvider.cleanupInactiveSessions();
      }
    }
    
    if (stream) {
      // Handle streaming response
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      
      await providerManager.streamCompletion(messages, options, (chunk) => {
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      });
      
      res.write('data: [DONE]\n\n');
      res.end();
    } else {
      // Handle regular response
      const response = await providerManager.createCompletion(messages, options);
      res.json(response);
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// API Keys Management
protectedRouter.get('/api-keys', async (req, res) => {
  try {
    const keys = await providerManager.getApiKeys();
    res.json({ keys });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.post('/api-keys', async (req, res) => {
  try {
    const { key, name, provider = 'gemini' } = req.body;
    
    if (!key) {
      return res.status(400).json({ error: 'API key is required' });
    }
    
    const result = await providerManager.addApiKey(key, name || 'API Key', provider);
    res.json({ success: true, key: result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.delete('/api-keys/:keyId', async (req, res) => {
  try {
    const { keyId } = req.params;
    const { provider = 'gemini' } = req.query;
    const result = await providerManager.deleteApiKey(keyId, provider);
    
    if (result) {
      res.json({ success: true, message: 'API key deleted' });
    } else {
      res.status(404).json({ error: 'API key not found' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Settings Management
protectedRouter.post('/settings', async (req, res) => {
  try {
    const { defaultModel, authMethod, provider, authToken, baseUrl } = req.body;
    
    const result = await providerManager.updateSettings({
      defaultModel,
      authMethod,
      authToken,
      baseUrl
    }, provider);
    
    res.json({ success: true, settings: result });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Token cache management endpoints
protectedRouter.get('/v1/debug/cache', async (req, res) => {
  try {
    const { tokenCache } = await import('./providers/tokenCache.js');
    const status = tokenCache.getStatus();
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.post('/v1/token/import', async (req, res) => {
  try {
    const { tokenCache } = await import('./providers/tokenCache.js');
    const success = await tokenCache.importFromGeminiCLI();
    
    if (success) {
      res.json({ success: true, message: 'OAuth credentials imported successfully' });
    } else {
      res.status(400).json({ error: 'Failed to import OAuth credentials' });
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.delete('/v1/token/clear', async (req, res) => {
  try {
    const { tokenCache } = await import('./providers/tokenCache.js');
    await tokenCache.clearAll();
    res.json({ success: true, message: 'All tokens cleared' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Vision test endpoint
protectedRouter.post('/v1/vision/test', async (req, res) => {
  try {
    const { image_url, model = 'gemini-2.5-flash' } = req.body;
    
    if (!image_url) {
      return res.status(400).json({ error: 'image_url is required' });
    }

    const response = await providerManager.createCompletion([
      {
        role: 'user',
        content: [
          {
            type: 'text',
            text: 'Describe what you see in this image.'
          },
          {
            type: 'image_url',
            image_url: { url: image_url }
          }
        ]
      }
    ], { model });

    res.json(response);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Thinking test endpoint
protectedRouter.post('/v1/thinking/test', async (req, res) => {
  try {
    const { message, model = 'gemini-2.5-pro', stream = false } = req.body;
    
    if (!message) {
      return res.status(400).json({ error: 'message is required' });
    }

    const messages = [{ role: 'user', content: message }];

    if (stream) {
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      
      await providerManager.streamCompletion(messages, { model }, (chunk) => {
        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
      });
      
      res.write('data: [DONE]\n\n');
      res.end();
    } else {
      const response = await providerManager.createCompletion(messages, { model });
      res.json(response);
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});


// Mount Dify routes
app.use('/dify', difyRoutes);

// Mount protected routes
app.use('/', protectedRouter);
 
// RBAC Management Endpoints
protectedRouter.get('/rbac/status', (req, res) => {
  try {
    const userRole = req.userRole || 'user';
    const permissions = rbacManager.getUserPermissions(userRole);
    
    res.json({
      success: true,
      current_user: {
        role: userRole,
        permissions: permissions.allowed_actions,
        description: permissions.description
      },
      available_roles: Object.keys(rbacManager.policies),
      dangerous_actions: rbacManager.dangerousActions,
      rbac_enabled: true
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.get('/rbac/policies', requirePermission('rbac_manage'), (req, res) => {
  try {
    res.json({
      success: true,
      policies: rbacManager.policies,
      dangerous_actions: rbacManager.dangerousActions
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.post('/rbac/test-permission', (req, res) => {
  try {
    const { action, role } = req.body;
    const testRole = role || req.userRole || 'user';
    
    if (!action) {
      return res.status(400).json({ error: 'action is required' });
    }
    
    const allowed = rbacManager.checkPermission(testRole, action);
    const isDangerous = rbacManager.isDangerousAction(action);
    
    res.json({
      success: true,
      role: testRole,
      action,
      allowed,
      is_dangerous: isDangerous,
      message: allowed ? 'Access granted' : 'Access denied'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Debug endpoint to see all registered routes
app.get('/debug/routes', (req, res) => {
  const routes = [];
  
  // Get routes from main app
  app._router.stack.forEach((middleware) => {
    if (middleware.route) {
      routes.push({
        path: middleware.route.path,
        methods: Object.keys(middleware.route.methods)
      });
    } else if (middleware.name === 'router') {
      // Get routes from mounted router
      middleware.handle.stack.forEach((handler) => {
        if (handler.route) {
          routes.push({
            path: handler.route.path,
            methods: Object.keys(handler.route.methods)
          });
        }
      });
    }
  });
  
  res.json({ routes });
});

// Debug endpoints for Phase 1 improvements
protectedRouter.get('/v1/debug/real-thinking', async (req, res) => {
  try {
    const { RealThinkingHandler } = await import('./providers/realThinkingHandler.js');
    const handler = new RealThinkingHandler();
    
    res.json({
      success: true,
      config: {
        enableRealThinking: handler.enableRealThinking,
        enableFakeThinking: handler.enableFakeThinking,
        streamThinkingAsContent: handler.streamThinkingAsContent,
        defaultThinkingBudget: handler.defaultThinkingBudget,
        thinkingModels: Array.from(handler.thinkingModels)
      },
      status: 'Real Thinking Handler initialized'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.get('/v1/debug/content-safety', async (req, res) => {
  try {
    const { ContentSafetyHandler } = await import('./providers/contentSafetyHandler.js');
    const handler = new ContentSafetyHandler();
    
    res.json({
      success: true,
      config: handler.config,
      safetyCategories: handler.safetyCategories,
      safetyThresholds: handler.safetyThresholds,
      status: 'Content Safety Handler initialized'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.get('/v1/debug/token-cache', async (req, res) => {
  try {
    const { tokenCache } = await import('./providers/tokenCache.js');
    const status = tokenCache.getStatus();
    const enhancedStats = tokenCache.getEnhancedStats();
    
    res.json({
      success: true,
      status,
      enhancedStats,
      config: tokenCache.config
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.post('/v1/debug/test-real-thinking', async (req, res) => {
  try {
    const { message = "What is 2+2? Think step by step.", model = 'gemini-2.5-pro' } = req.body;
    
    const messages = [{ role: 'user', content: message }];
    const options = {
      model,
      thinking_budget: 10000,
      reasoning_effort: 'medium'
    };
    
    const response = await providerManager.createCompletion(messages, options);
    res.json({
      success: true,
      test: 'Real Thinking',
      response
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

protectedRouter.post('/v1/debug/test-content-safety', async (req, res) => {
  try {
    const { message = "Tell me about safety measures.", model = 'gemini-2.5-flash' } = req.body;

    const messages = [{ role: 'user', content: message }];
    const options = {
      model,
      safety_settings: {
        harassment: 'BLOCK_SOME',
        hate_speech: 'BLOCK_SOME',
        sexually_explicit: 'BLOCK_SOME',
        dangerous_content: 'BLOCK_SOME'
      }
    };

    const response = await providerManager.createCompletion(messages, options);
    res.json({
      success: true,
      test: 'Content Safety',
      response
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Debug endpoint for cursor sessions
protectedRouter.get('/v1/debug/cursor-sessions', async (req, res) => {
  try {
    const cursorProvider = providerManager.providers.get('cursor');
    if (!cursorProvider) {
      return res.status(404).json({ error: 'Cursor provider not found' });
    }

    const sessions = cursorProvider.getAllSessions ? cursorProvider.getAllSessions() : [];
    const activeCount = cursorProvider.getActiveSessionCount ? cursorProvider.getActiveSessionCount() : 0;

    res.json({
      success: true,
      activeSessionCount: activeCount,
      sessions: sessions,
      provider: 'cursor'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Debug endpoint to cleanup cursor sessions manually
protectedRouter.post('/v1/debug/cursor-cleanup', async (req, res) => {
  try {
    const cursorProvider = providerManager.providers.get('cursor');
    if (!cursorProvider) {
      return res.status(404).json({ error: 'Cursor provider not found' });
    }

    const { maxAge } = req.body;
    let cleanedCount = 0;

    if (cursorProvider.cleanupInactiveSessions) {
      cleanedCount = cursorProvider.cleanupInactiveSessions(maxAge);
    }

    res.json({
      success: true,
      cleanedSessionsCount: cleanedCount,
      message: `Cleaned up ${cleanedCount} inactive sessions`
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Error handling middleware
app.use(errorHandler);

// Start server
app.listen(PORT, '0.0.0.0', () => {
  logger.info(`🚀 Gemini CLI Wrapper running on port ${PORT}`);
  logger.info(`📊 Health check: http://localhost:${PORT}/health`);
  logger.info(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Get server IP for external access
  const nets = os.networkInterfaces();
  const results = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }
  
  // Log external access URLs
  for (const [dev, addresses] of Object.entries(results)) {
    for (const addr of addresses) {
      logger.info(`🌐 External access: http://${addr}:${PORT}`);
    }
  }
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');

  // Cleanup cursor provider sessions
  const cursorProvider = providerManager.providers.get('cursor');
  if (cursorProvider && cursorProvider.cleanupAllSessions) {
    cursorProvider.cleanupAllSessions();
  }

  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');

  // Cleanup cursor provider sessions
  const cursorProvider = providerManager.providers.get('cursor');
  if (cursorProvider && cursorProvider.cleanupAllSessions) {
    cursorProvider.cleanupAllSessions();
  }

  process.exit(0);
});

export default app;