import { spawn } from 'child_process';
import fs from 'fs/promises';
import path from 'path';
import { logger } from '../utils/logger.js';
import { openWebUITools } from './openWebUITools.js';
import { puppeteerTools } from './puppeteerTools.js';
import { rbacManager } from '../middleware/rbac.js';
// Lazy import to avoid circular dependency
// import { GeminiCLIProvider } from '../providers/geminiProvider.js';

/**
 * MCP-style Tool Registry
 * Manages tools that can be called by the AI model
 */
export class ToolRegistry {
  constructor() {
    this.tools = new Map();
    this.geminiProvider = null; // Lazy initialization
    this.registerBuiltinTools();
  }

  /**
   * Get Gemini provider instance (lazy loading)
   */
  async getGeminiProvider() {
    if (!this.geminiProvider) {
      // Dynamic import to avoid circular dependency
      const { GeminiCLIProvider } = await import('../providers/geminiProvider.js');
      this.geminiProvider = new GeminiCLIProvider();
    }
    return this.geminiProvider;
  }

  /**
   * Register a tool following MCP pattern
   */
  registerTool(name, definition) {
    this.tools.set(name, {
      name,
      description: definition.description,
      inputSchema: definition.inputSchema,
      handler: definition.handler,
      category: definition.category || 'general'
    });
    logger.debug(`Registered tool: ${name}`);
  }

  /**
   * Get all available tools in MCP format
   */
  listTools() {
    return Array.from(this.tools.values()).map(tool => ({
      name: tool.name,
      description: tool.description,
      inputSchema: tool.inputSchema,
      category: tool.category
    }));
  }

  /**
   * Execute a tool with given arguments and RBAC authorization
   */
  async executeTool(name, args, userContext = null) {
    const tool = this.tools.get(name);
    if (!tool) {
      throw new Error(`Tool '${name}' not found`);
    }

    // RBAC Authorization Check
    if (userContext && userContext.userRole) {
      const userRole = userContext.userRole;
      const allowed = rbacManager.checkPermission(userRole, name);
      
      // Log the access attempt
      const logData = {
        user_role: userRole,
        action: name,
        allowed,
        args: JSON.stringify(args),
        ip: userContext.ip || 'unknown',
        timestamp: new Date().toISOString()
      };

      if (!allowed) {
        logger.warn(`❌ RBAC: ${userRole} denied access to tool '${name}'`, logData);
        throw new Error(`Access denied: Role '${userRole}' is not authorized to execute tool '${name}'. Required role: admin`);
      }

      // Log successful authorization
      logger.info(`✅ RBAC: ${userRole} authorized to execute tool '${name}'`, logData);
      
      // Extra warning for dangerous actions
      if (rbacManager.isDangerousAction(name)) {
        logger.warn(`🚨 DANGEROUS ACTION: ${userRole} executing ${name}`, logData);
      }
    } else {
      // If no user context provided, check if this is a dangerous action
      if (rbacManager.isDangerousAction(name)) {
        logger.warn(`⚠️ Executing dangerous tool '${name}' without user context - this should not happen in production`);
      }
    }

    logger.debug(`Executing tool: ${name}`, args);
    try {
      const result = await tool.handler(args);
      logger.debug(`Tool ${name} completed successfully`);
      return result;
    } catch (error) {
      logger.error(`Tool ${name} failed:`, error);
      throw error;
    }
  }

  /**
   * Check if unrestricted mode is enabled
   */
  isUnrestrictedMode() {
    return process.env.GEMINI_UNRESTRICTED_MODE === 'true';
  }

  /**
   * Register built-in tools
   */
  registerBuiltinTools() {
    // Web Search Tool
    this.registerTool('web_search', {
      description: 'Search the web for current information',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'The search query'
          },
          max_results: {
            type: 'number',
            description: 'Maximum number of results to return',
            default: 5
          }
        },
        required: ['query']
      },
      category: 'web',
      handler: this.webSearchHandler.bind(this)
    });

    // File Read Tool
    this.registerTool('read_file', {
      description: 'Read and analyze a file in the project directory (or anywhere if unrestricted mode is enabled)',
      inputSchema: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to the file (relative or absolute if unrestricted)'
          },
          encoding: {
            type: 'string',
            description: 'File encoding',
            default: 'utf8'
          }
        },
        required: ['path']
      },
      category: 'filesystem',
      handler: this.readFileHandler.bind(this)
    });

    // File Write Tool
    this.registerTool('write_file', {
      description: 'Write content to a file in the project directory (or anywhere if unrestricted mode is enabled)',
      inputSchema: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to the file (relative or absolute if unrestricted)'
          },
          content: {
            type: 'string',
            description: 'Content to write to the file'
          },
          encoding: {
            type: 'string',
            description: 'File encoding',
            default: 'utf8'
          }
        },
        required: ['path', 'content']
      },
      category: 'filesystem',
      handler: this.writeFileHandler.bind(this)
    });

    // Execute Command Tool
    this.registerTool('execute_command', {
      description: 'Execute a shell command (safely restricted by default, unrestricted if enabled)',
      inputSchema: {
        type: 'object',
        properties: {
          command: {
            type: 'string',
            description: 'The command to execute'
          },
          timeout: {
            type: 'number',
            description: 'Timeout in milliseconds',
            default: 30000
          },
          working_directory: {
            type: 'string',
            description: 'Working directory for command execution'
          }
        },
        required: ['command']
      },
      category: 'system',
      handler: this.executeCommandHandler.bind(this)
    });

    // Code Analysis Tool
    this.registerTool('analyze_code', {
      description: 'Analyze code structure and dependencies',
      inputSchema: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Path to analyze (file or directory)'
          },
          analysis_type: {
            type: 'string',
            description: 'Type of analysis to perform',
            enum: ['structure', 'dependencies', 'complexity', 'security']
          }
        },
        required: ['path']
      },
      category: 'development',
      handler: this.analyzeCodeHandler.bind(this)
    });

    // Memory Tool (for conversation context)
    this.registerTool('store_memory', {
      description: 'Store information for later retrieval',
      inputSchema: {
        type: 'object',
        properties: {
          key: {
            type: 'string',
            description: 'Memory key'
          },
          value: {
            type: 'string',
            description: 'Value to store'
          },
          ttl: {
            type: 'number',
            description: 'Time to live in seconds',
            default: 3600
          }
        },
        required: ['key', 'value']
      },
      category: 'memory',
      handler: this.storeMemoryHandler.bind(this)
    });

    // Register Open WebUI integration tools
    try {
      openWebUITools.registerTools(this);
      logger.info('✅ Open WebUI integration tools registered');
    } catch (error) {
      logger.warn('⚠️ Failed to register Open WebUI tools:', error.message);
    }

    // Register Puppeteer browser automation tools
    try {
      puppeteerTools.registerTools(this);
      logger.info('✅ Puppeteer browser automation tools registered');
    } catch (error) {
      logger.warn('⚠️ Failed to register Puppeteer tools:', error.message);
    }

    // Register new Gemini CLI tools that were missing
    this.registerNewGeminiTools();

    logger.info(`Registered ${this.tools.size} built-in tools`);
    if (this.isUnrestrictedMode()) {
      logger.warn('🚨 UNRESTRICTED MODE ENABLED - Full system access allowed');
    } else {
      logger.info('🔒 Restricted mode - Limited to project directory and safe commands');
    }
  }

  // Tool Handlers
  async webSearchHandler(args) {
    const { query, max_results = 5 } = args;
    
    return new Promise((resolve, reject) => {
      const searchProcess = spawn('gemini', ['-p', `Search the web for: ${query}. Provide ${max_results} relevant results.`], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let output = '';
      let errorOutput = '';

      searchProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      searchProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      searchProcess.on('close', (code) => {
        if (code === 0) {
          resolve({
            success: true,
            query,
            results: output.trim(),
            source: 'web_search'
          });
        } else {
          reject(new Error(`Web search failed: ${errorOutput}`));
        }
      });

      searchProcess.stdin.end();
    });
  }

  async readFileHandler(args) {
    const { path: filePath, encoding = 'utf8' } = args;
    
    let fullPath;
    
    if (this.isUnrestrictedMode()) {
      // In unrestricted mode, allow absolute paths
      fullPath = path.isAbsolute(filePath) ? filePath : path.resolve(process.cwd(), filePath);
      logger.debug(`🚨 Unrestricted file read: ${fullPath}`);
    } else {
      // Security check - only allow files in project directory
      fullPath = path.resolve(process.cwd(), filePath);
      if (!fullPath.startsWith(process.cwd())) {
        throw new Error('Access denied: File outside project directory. Set GEMINI_UNRESTRICTED_MODE=true to allow.');
      }
    }

    try {
      const content = await fs.readFile(fullPath, encoding);
      const stats = await fs.stat(fullPath);
      
      return {
        success: true,
        path: filePath,
        fullPath,
        content,
        size: stats.size,
        modified: stats.mtime,
        unrestricted: this.isUnrestrictedMode(),
        source: 'read_file'
      };
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }

  async writeFileHandler(args) {
    const { path: filePath, content, encoding = 'utf8' } = args;
    
    let fullPath;
    
    if (this.isUnrestrictedMode()) {
      // In unrestricted mode, allow absolute paths
      fullPath = path.isAbsolute(filePath) ? filePath : path.resolve(process.cwd(), filePath);
      logger.debug(`🚨 Unrestricted file write: ${fullPath}`);
    } else {
      // Security check
      fullPath = path.resolve(process.cwd(), filePath);
      if (!fullPath.startsWith(process.cwd())) {
        throw new Error('Access denied: File outside project directory. Set GEMINI_UNRESTRICTED_MODE=true to allow.');
      }
    }

    try {
      // Create directory if it doesn't exist
      await fs.mkdir(path.dirname(fullPath), { recursive: true });
      await fs.writeFile(fullPath, content, encoding);
      
      return {
        success: true,
        path: filePath,
        fullPath,
        size: content.length,
        unrestricted: this.isUnrestrictedMode(),
        source: 'write_file'
      };
    } catch (error) {
      throw new Error(`Failed to write file: ${error.message}`);
    }
  }

  async executeCommandHandler(args) {
    const { command, timeout = 30000, working_directory } = args;
    
    if (this.isUnrestrictedMode()) {
      logger.warn(`🚨 Unrestricted command execution: ${command}`);
      
      // In unrestricted mode, allow any command
      return new Promise((resolve, reject) => {
        const options = {
          stdio: ['pipe', 'pipe', 'pipe'],
          timeout
        };
        
        if (working_directory) {
          options.cwd = working_directory;
        }
        
        const childProcess = spawn('bash', ['-c', command], options);

        let stdout = '';
        let stderr = '';

        childProcess.stdout.on('data', (data) => {
          stdout += data.toString();
        });

        childProcess.stderr.on('data', (data) => {
          stderr += data.toString();
        });

        childProcess.on('close', (code) => {
          resolve({
            success: code === 0,
            command,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            exitCode: code,
            unrestricted: true,
            workingDirectory: working_directory || process.cwd(),
            source: 'execute_command'
          });
        });

        childProcess.on('error', (error) => {
          reject(new Error(`Command execution failed: ${error.message}`));
        });
      });
    } else {
      // Security: whitelist safe commands
      const safeCommands = ['ls', 'pwd', 'cat', 'grep', 'find', 'head', 'tail', 'wc', 'echo', 'date', 'whoami', 'df', 'free', 'ps', 'top', 'uname'];
      const cmdParts = command.trim().split(' ');
      const baseCmd = cmdParts[0];
      
      if (!safeCommands.includes(baseCmd)) {
        throw new Error(`Command '${baseCmd}' not allowed for security reasons. Set GEMINI_UNRESTRICTED_MODE=true to allow all commands.`);
      }

      return new Promise((resolve, reject) => {
        const options = {
          stdio: ['pipe', 'pipe', 'pipe'],
          timeout
        };
        
        // In restricted mode, always use project directory
        options.cwd = process.cwd();
        
        const childProcess = spawn('bash', ['-c', command], options);

        let stdout = '';
        let stderr = '';

        childProcess.stdout.on('data', (data) => {
          stdout += data.toString();
        });

        childProcess.stderr.on('data', (data) => {
          stderr += data.toString();
        });

        childProcess.on('close', (code) => {
          resolve({
            success: code === 0,
            command,
            stdout: stdout.trim(),
            stderr: stderr.trim(),
            exitCode: code,
            unrestricted: false,
            workingDirectory: process.cwd(),
            source: 'execute_command'
          });
        });

        childProcess.on('error', (error) => {
          reject(new Error(`Command execution failed: ${error.message}`));
        });
      });
    }
  }

  async analyzeCodeHandler(args) {
    const { path: targetPath, analysis_type = 'structure' } = args;
    
    let fullPath;
    
    if (this.isUnrestrictedMode()) {
      fullPath = path.isAbsolute(targetPath) ? targetPath : path.resolve(process.cwd(), targetPath);
    } else {
      fullPath = path.resolve(process.cwd(), targetPath);
      if (!fullPath.startsWith(process.cwd())) {
        throw new Error('Access denied: Path outside project directory. Set GEMINI_UNRESTRICTED_MODE=true to allow.');
      }
    }

    try {
      const stats = await fs.stat(fullPath);
      let result = {
        success: true,
        path: targetPath,
        fullPath,
        type: stats.isDirectory() ? 'directory' : 'file',
        analysis_type,
        unrestricted: this.isUnrestrictedMode(),
        source: 'analyze_code'
      };

      switch (analysis_type) {
        case 'structure':
          if (stats.isDirectory()) {
            const files = await this.getDirectoryStructure(fullPath);
            result.structure = files;
          } else {
            const content = await fs.readFile(fullPath, 'utf8');
            result.lines = content.split('\n').length;
            result.size = stats.size;
          }
          break;

        case 'dependencies':
          if (targetPath.includes('package.json')) {
            const content = await fs.readFile(fullPath, 'utf8');
            const pkg = JSON.parse(content);
            result.dependencies = pkg.dependencies || {};
            result.devDependencies = pkg.devDependencies || {};
          }
          break;

        default:
          result.analysis = `Analysis type '${analysis_type}' not yet implemented`;
      }

      return result;
    } catch (error) {
      throw new Error(`Code analysis failed: ${error.message}`);
    }
  }

  async storeMemoryHandler(args) {
    const { key, value, ttl = 3600 } = args;
    
    // Simple in-memory storage (in production, use Redis or similar)
    if (!this.memory) {
      this.memory = new Map();
    }

    const expiresAt = Date.now() + (ttl * 1000);
    this.memory.set(key, { value, expiresAt });

    // Cleanup expired entries
    for (const [k, v] of this.memory.entries()) {
      if (v.expiresAt < Date.now()) {
        this.memory.delete(k);
      }
    }

    return {
      success: true,
      key,
      stored: true,
      expiresAt: new Date(expiresAt).toISOString(),
      source: 'store_memory'
    };
  }

  async getDirectoryStructure(dirPath, maxDepth = 3, currentDepth = 0) {
    if (currentDepth >= maxDepth) return [];

    try {
      const items = await fs.readdir(dirPath);
      const structure = [];

      for (const item of items) {
        if (item.startsWith('.')) continue; // Skip hidden files
        
        const itemPath = path.join(dirPath, item);
        const stats = await fs.stat(itemPath);
        
        const structureItem = {
          name: item,
          type: stats.isDirectory() ? 'directory' : 'file',
          size: stats.size,
          modified: stats.mtime
        };

        if (stats.isDirectory() && currentDepth < maxDepth - 1) {
          structureItem.children = await this.getDirectoryStructure(itemPath, maxDepth, currentDepth + 1);
        }

        structure.push(structureItem);
      }

      return structure;
    } catch (error) {
      logger.warn(`Failed to read directory ${dirPath}:`, error.message);
      return [];
    }
  }

  // Memory retrieval helper
  getMemory(key) {
    if (!this.memory) return null;
    
    const stored = this.memory.get(key);
    if (!stored) return null;
    
    if (stored.expiresAt < Date.now()) {
      this.memory.delete(key);
      return null;
    }
    
    return stored.value;
  }

  /**
   * Register new Gemini CLI tools that were missing from MCP server
   */
  registerNewGeminiTools() {
    // List Directory Tool
    this.registerTool('list_directory', {
      description: 'Lists files and subdirectories in a given directory. Can optionally ignore entries matching provided glob patterns.',
      inputSchema: {
        type: 'object',
        properties: {
          path: {
            type: 'string',
            description: 'Directory path to list (relative or absolute if unrestricted)',
            default: '.'
          },
          ignore_patterns: {
            type: 'array',
            items: { type: 'string' },
            description: 'Glob patterns to ignore (e.g., ["*.log", "node_modules"])'
          }
        },
        required: ['path']
      },
      category: 'filesystem',
      handler: this.listDirectoryHandler.bind(this)
    });

    // Search File Content Tool
    this.registerTool('search_file_content', {
      description: 'Searches for a regular expression pattern within the content of files in a specified directory. Returns lines containing matches with file paths and line numbers.',
      inputSchema: {
        type: 'object',
        properties: {
          pattern: {
            type: 'string',
            description: 'Regular expression pattern to search for'
          },
          directory: {
            type: 'string',
            description: 'Directory to search in (default: current working directory)',
            default: '.'
          },
          file_pattern: {
            type: 'string',
            description: 'Glob pattern to filter files (e.g., "*.js", "*.md")',
            default: '*'
          },
          recursive: {
            type: 'boolean',
            description: 'Search recursively in subdirectories',
            default: true
          }
        },
        required: ['pattern']
      },
      category: 'filesystem',
      handler: this.searchFileContentHandler.bind(this)
    });

    // Glob Tool
    this.registerTool('glob', {
      description: 'Efficiently finds files matching specific glob patterns (e.g., src/**/*.ts, **/*.md), returning absolute paths sorted by modification time (newest first).',
      inputSchema: {
        type: 'object',
        properties: {
          pattern: {
            type: 'string',
            description: 'Glob pattern to match files (e.g., "src/**/*.js", "**/*.md")'
          },
          base_directory: {
            type: 'string',
            description: 'Base directory to search from',
            default: '.'
          },
          sort_by_time: {
            type: 'boolean',
            description: 'Sort results by modification time (newest first)',
            default: true
          }
        },
        required: ['pattern']
      },
      category: 'filesystem',
      handler: this.globHandler.bind(this)
    });

    // Web Fetch Tool
    this.registerTool('web_fetch', {
      description: 'Processes content from URL(s), including local and private network addresses (e.g., localhost). Can handle up to 20 URLs with specific instructions.',
      inputSchema: {
        type: 'object',
        properties: {
          urls: {
            type: 'array',
            items: { type: 'string' },
            description: 'Array of URLs to fetch (up to 20)',
            maxItems: 20
          },
          instructions: {
            type: 'string',
            description: 'Instructions for processing the content (e.g., "summarize", "extract specific data")'
          },
          timeout: {
            type: 'number',
            description: 'Request timeout in milliseconds',
            default: 10000
          }
        },
        required: ['urls']
      },
      category: 'web',
      handler: this.webFetchHandler.bind(this)
    });

    // Read Many Files Tool
    this.registerTool('read_many_files', {
      description: 'Reads content from multiple files specified by paths or glob patterns. Concatenates text files and can process images/PDFs if explicitly requested.',
      inputSchema: {
        type: 'object',
        properties: {
          paths: {
            type: 'array',
            items: { type: 'string' },
            description: 'Array of file paths or glob patterns'
          },
          target_directory: {
            type: 'string',
            description: 'Target directory for relative paths',
            default: '.'
          },
          include_metadata: {
            type: 'boolean',
            description: 'Include file metadata (size, modified time)',
            default: true
          }
        },
        required: ['paths']
      },
      category: 'filesystem',
      handler: this.readManyFilesHandler.bind(this)
    });

    // Fact Check Tool using Gemini CLI
    this.registerTool('fact_check', {
      description: 'Fact-checks a statement or claim using Gemini CLI for accurate verification',
      inputSchema: {
        type: 'object',
        properties: {
          statement: {
            type: 'string',
            description: 'The statement or claim to fact-check'
          },
          context: {
            type: 'string',
            description: 'Additional context for the fact-check (optional)'
          },
          detailed: {
            type: 'boolean',
            description: 'Whether to provide detailed analysis',
            default: true
          }
        },
        required: ['statement']
      },
      category: 'analysis',
      handler: this.factCheckHandler.bind(this)
    });

    // Research Topic Tool using Gemini CLI
    this.registerTool('research_topic', {
      description: 'Research a topic comprehensively using Gemini CLI',
      inputSchema: {
        type: 'object',
        properties: {
          topic: {
            type: 'string',
            description: 'The topic to research'
          },
          focus_areas: {
            type: 'array',
            items: { type: 'string' },
            description: 'Specific areas to focus on in the research'
          },
          depth: {
            type: 'string',
            enum: ['basic', 'detailed', 'comprehensive'],
            description: 'Level of research depth',
            default: 'detailed'
          }
        },
        required: ['topic']
      },
      category: 'analysis',
      handler: this.researchTopicHandler.bind(this)
    });

    logger.info('✅ New Gemini CLI tools registered: list_directory, search_file_content, glob, web_fetch, read_many_files, fact_check, research_topic');
  }

  // New tool handlers
  async listDirectoryHandler(args) {
    const { path: dirPath = '.', ignore_patterns = [] } = args;
    
    let fullPath;
    if (this.isUnrestrictedMode()) {
      fullPath = path.isAbsolute(dirPath) ? dirPath : path.resolve(process.cwd(), dirPath);
    } else {
      fullPath = path.resolve(process.cwd(), dirPath);
      if (!fullPath.startsWith(process.cwd())) {
        throw new Error('Access denied: Directory outside project directory. Set GEMINI_UNRESTRICTED_MODE=true to allow.');
      }
    }

    try {
      const items = await fs.readdir(fullPath, { withFileTypes: true });
      const results = [];

      for (const item of items) {
        // Check ignore patterns
        const shouldIgnore = ignore_patterns.some(pattern => {
          const regex = new RegExp(pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
          return regex.test(item.name);
        });

        if (!shouldIgnore) {
          const itemPath = path.join(fullPath, item.name);
          const stats = await fs.stat(itemPath);
          
          results.push({
            name: item.name,
            type: item.isDirectory() ? 'directory' : 'file',
            size: stats.size,
            modified: stats.mtime,
            path: path.relative(process.cwd(), itemPath)
          });
        }
      }

      return {
        success: true,
        directory: dirPath,
        fullPath,
        items: results,
        count: results.length,
        unrestricted: this.isUnrestrictedMode(),
        source: 'list_directory'
      };
    } catch (error) {
      throw new Error(`Failed to list directory: ${error.message}`);
    }
  }

  async searchFileContentHandler(args) {
    const { pattern, directory = '.', file_pattern = '*', recursive = true } = args;
    
    let fullPath;
    if (this.isUnrestrictedMode()) {
      fullPath = path.isAbsolute(directory) ? directory : path.resolve(process.cwd(), directory);
    } else {
      fullPath = path.resolve(process.cwd(), directory);
      if (!fullPath.startsWith(process.cwd())) {
        throw new Error('Access denied: Directory outside project directory. Set GEMINI_UNRESTRICTED_MODE=true to allow.');
      }
    }

    try {
      const regex = new RegExp(pattern, 'gi');
      const fileRegex = new RegExp(file_pattern.replace(/\*/g, '.*').replace(/\?/g, '.'));
      const results = [];

      const searchInDirectory = async (dirPath, currentDepth = 0) => {
        const items = await fs.readdir(dirPath, { withFileTypes: true });
        
        for (const item of items) {
          const itemPath = path.join(dirPath, item.name);
          
          if (item.isDirectory() && recursive) {
            await searchInDirectory(itemPath, currentDepth + 1);
          } else if (item.isFile() && fileRegex.test(item.name)) {
            try {
              const content = await fs.readFile(itemPath, 'utf8');
              const lines = content.split('\n');
              
              lines.forEach((line, lineNumber) => {
                const matches = line.match(regex);
                if (matches) {
                  results.push({
                    file: path.relative(process.cwd(), itemPath),
                    line: lineNumber + 1,
                    content: line.trim(),
                    matches: matches
                  });
                }
              });
            } catch (error) {
              // Skip files that can't be read as text
              logger.debug(`Skipping file ${itemPath}: ${error.message}`);
            }
          }
        }
      };

      await searchInDirectory(fullPath);

      return {
        success: true,
        pattern,
        directory,
        file_pattern,
        recursive,
        matches: results,
        count: results.length,
        unrestricted: this.isUnrestrictedMode(),
        source: 'search_file_content'
      };
    } catch (error) {
      throw new Error(`Search failed: ${error.message}`);
    }
  }

  async globHandler(args) {
    const { pattern, base_directory = '.', sort_by_time = true } = args;
    
    let fullBasePath;
    if (this.isUnrestrictedMode()) {
      fullBasePath = path.isAbsolute(base_directory) ? base_directory : path.resolve(process.cwd(), base_directory);
    } else {
      fullBasePath = path.resolve(process.cwd(), base_directory);
      if (!fullBasePath.startsWith(process.cwd())) {
        throw new Error('Access denied: Directory outside project directory. Set GEMINI_UNRESTRICTED_MODE=true to allow.');
      }
    }

    try {
      // Simple glob implementation
      const results = [];
      const globRegex = new RegExp(
        pattern
          .replace(/\*\*/g, '.*')
          .replace(/\*/g, '[^/]*')
          .replace(/\?/g, '[^/]')
      );

      const searchGlob = async (dirPath, relativePath = '') => {
        try {
          const items = await fs.readdir(dirPath, { withFileTypes: true });
          
          for (const item of items) {
            const itemPath = path.join(dirPath, item.name);
            const relativeItemPath = path.join(relativePath, item.name);
            
            if (item.isDirectory()) {
              await searchGlob(itemPath, relativeItemPath);
            } else if (globRegex.test(relativeItemPath)) {
              const stats = await fs.stat(itemPath);
              results.push({
                path: itemPath,
                relativePath: relativeItemPath,
                size: stats.size,
                modified: stats.mtime
              });
            }
          }
        } catch (error) {
          // Skip directories we can't read
          logger.debug(`Skipping directory ${dirPath}: ${error.message}`);
        }
      };

      await searchGlob(fullBasePath);

      // Sort by modification time if requested
      if (sort_by_time) {
        results.sort((a, b) => b.modified.getTime() - a.modified.getTime());
      }

      return {
        success: true,
        pattern,
        base_directory,
        matches: results.map(r => ({
          path: r.path,
          relativePath: r.relativePath,
          size: r.size,
          modified: r.modified
        })),
        count: results.length,
        sorted_by_time: sort_by_time,
        unrestricted: this.isUnrestrictedMode(),
        source: 'glob'
      };
    } catch (error) {
      throw new Error(`Glob search failed: ${error.message}`);
    }
  }

  async webFetchHandler(args) {
    const { urls, instructions, timeout = 10000 } = args;
    
    if (urls.length > 20) {
      throw new Error('Maximum 20 URLs allowed per request');
    }

    const results = [];
    
    for (const url of urls) {
      try {
        // Use Gemini CLI to fetch and process the URL
        const fetchResult = await new Promise((resolve, reject) => {
          const prompt = instructions
            ? `Fetch and process the content from ${url}. Instructions: ${instructions}`
            : `Fetch and return the content from ${url}`;
            
          const fetchProcess = spawn('gemini', ['-p', prompt], {
            stdio: ['pipe', 'pipe', 'pipe'],
            timeout
          });

          let output = '';
          let errorOutput = '';

          fetchProcess.stdout.on('data', (data) => {
            output += data.toString();
          });

          fetchProcess.stderr.on('data', (data) => {
            errorOutput += data.toString();
          });

          fetchProcess.on('close', (code) => {
            if (code === 0) {
              resolve({
                success: true,
                url,
                content: output.trim(),
                processed: !!instructions
              });
            } else {
              resolve({
                success: false,
                url,
                error: errorOutput || 'Fetch failed',
                processed: false
              });
            }
          });

          fetchProcess.on('error', (error) => {
            resolve({
              success: false,
              url,
              error: error.message,
              processed: false
            });
          });

          fetchProcess.stdin.end();
        });

        results.push(fetchResult);
      } catch (error) {
        results.push({
          success: false,
          url,
          error: error.message,
          processed: false
        });
      }
    }

    return {
      success: true,
      urls: urls,
      instructions,
      results,
      successful_fetches: results.filter(r => r.success).length,
      failed_fetches: results.filter(r => !r.success).length,
      source: 'web_fetch'
    };
  }

  async readManyFilesHandler(args) {
    const { paths, target_directory = '.', include_metadata = true } = args;
    
    let fullTargetPath;
    if (this.isUnrestrictedMode()) {
      fullTargetPath = path.isAbsolute(target_directory) ? target_directory : path.resolve(process.cwd(), target_directory);
    } else {
      fullTargetPath = path.resolve(process.cwd(), target_directory);
      if (!fullTargetPath.startsWith(process.cwd())) {
        throw new Error('Access denied: Directory outside project directory. Set GEMINI_UNRESTRICTED_MODE=true to allow.');
      }
    }

    const results = [];
    let concatenatedContent = '';

    for (const filePath of paths) {
      try {
        let fullPath;
        
        // Handle glob patterns
        if (filePath.includes('*') || filePath.includes('?')) {
          const globResults = await this.globHandler({
            pattern: filePath,
            base_directory: target_directory
          });
          
          for (const match of globResults.matches) {
            const content = await fs.readFile(match.path, 'utf8');
            const fileResult = {
              path: match.relativePath,
              fullPath: match.path,
              content,
              size: match.size,
              modified: match.modified,
              success: true
            };
            
            if (!include_metadata) {
              delete fileResult.size;
              delete fileResult.modified;
            }
            
            results.push(fileResult);
            concatenatedContent += `\n--- ${match.relativePath} ---\n${content}\n`;
          }
          continue;
        }

        // Handle regular file paths
        if (path.isAbsolute(filePath)) {
          fullPath = filePath;
        } else {
          fullPath = path.resolve(fullTargetPath, filePath);
        }

        if (!this.isUnrestrictedMode() && !fullPath.startsWith(process.cwd())) {
          throw new Error('Access denied: File outside project directory');
        }

        const stats = await fs.stat(fullPath);
        const content = await fs.readFile(fullPath, 'utf8');
        
        const fileResult = {
          path: filePath,
          fullPath,
          content,
          size: stats.size,
          modified: stats.mtime,
          success: true
        };

        if (!include_metadata) {
          delete fileResult.size;
          delete fileResult.modified;
        }

        results.push(fileResult);
        concatenatedContent += `\n--- ${filePath} ---\n${content}\n`;

      } catch (error) {
        results.push({
          path: filePath,
          success: false,
          error: error.message
        });
      }
    }

    return {
      success: true,
      target_directory,
      paths,
      files: results,
      concatenated_content: concatenatedContent.trim(),
      successful_reads: results.filter(r => r.success).length,
      failed_reads: results.filter(r => !r.success).length,
      include_metadata,
      unrestricted: this.isUnrestrictedMode(),
      source: 'read_many_files'
    };
  }

  /**
   * Fact-check a statement using Gemini CLI
   */
  async factCheckHandler(args) {
    const { statement, context, detailed = true } = args;

    try {
      const timestamp = new Date().toISOString();

      // Use Google Search grounding built into Gemini CLI for fact checking
      let prompt = `Use Google Search to fact-check this statement: "${statement}"`;

      if (context) {
        prompt += `\n\nContext: ${context}`;
      }

      if (detailed) {
        prompt += `\n\nProvide detailed analysis with:
1. Search results verification
2. True/False/Partially True assessment
3. Supporting evidence from reliable sources
4. Corrections if statement is false`;
      } else {
        prompt += `\n\nBrief fact-check with search verification: True/False/Partially True and key evidence.`;
      }

      // Execute using internal Gemini provider with search grounding
      const geminiProvider = await this.getGeminiProvider();
      const result = await geminiProvider.executeGeminiCommand(prompt, {
        model: 'gemini-2.5-flash',
        temperature: 0.1, // Lower temperature for factual accuracy
        max_tokens: detailed ? 3000 : 1500
      });

      return {
        statement,
        fact_check_type: 'search_grounded_verification',
        timestamp,
        analysis: result || 'No analysis returned from Gemini CLI',
        context: context || null,
        detailed,
        source: 'gemini_cli_google_search_grounding',
        success: true
      };

    } catch (error) {
      logger.error('Fact check failed:', error);
      return {
        statement,
        fact_check_type: 'search_grounded_verification',
        timestamp: new Date().toISOString(),
        analysis: `Error in fact-checking: ${error.message}`,
        context: context || null,
        detailed,
        source: 'gemini_cli_google_search_grounding',
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Research a topic using Gemini CLI
   */
  async researchTopicHandler(args) {
    const { topic, focus_areas = [], depth = 'detailed' } = args;

    try {
      const timestamp = new Date().toISOString();

      // Use Google Search grounding for comprehensive research
      let prompt = `Use Google Search to research this topic: "${topic}"`;

      if (focus_areas.length > 0) {
        prompt += `\n\nFocus search on these areas: ${focus_areas.join(', ')}`;
      }

      switch (depth) {
        case 'basic':
          prompt += `\n\nProvide basic overview with search-verified facts covering key points and essential information.`;
          break;
        case 'comprehensive':
          prompt += `\n\nProvide comprehensive research using extensive search results including:
1. Detailed background and context from multiple sources
2. Current state and recent developments
3. Different perspectives and viewpoints
4. Key statistics and data with sources
5. Future implications and trends
6. Relevant sources and references`;
          break;
        default: // detailed
          prompt += `\n\nProvide detailed research summary using search results including:
1. Background and context
2. Key facts and current information
3. Important developments
4. Different perspectives if applicable`;
      }

      // Execute using internal Gemini provider with search grounding
      const geminiProvider = await this.getGeminiProvider();
      const result = await geminiProvider.executeGeminiCommand(prompt, {
        model: 'gemini-2.5-flash',
        temperature: 0.1, // Lower temperature for factual research
        max_tokens: depth === 'comprehensive' ? 5000 : 3000
      });

      return {
        topic,
        focus_areas,
        depth,
        timestamp,
        research_results: result || 'No research results returned from Gemini CLI',
        source: 'gemini_cli_google_search_research',
        success: true
      };

    } catch (error) {
      logger.error('Research failed:', error);
      return {
        topic,
        focus_areas,
        depth,
        timestamp: new Date().toISOString(),
        research_results: `Error in research: ${error.message}`,
        source: 'gemini_cli_google_search_research',
        success: false,
        error: error.message
      };
    }
  }
}

export const toolRegistry = new ToolRegistry();