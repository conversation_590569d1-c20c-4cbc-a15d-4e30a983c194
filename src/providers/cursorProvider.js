import { EventEmitter } from 'events';
import { spawn } from 'child_process';
import { logger } from '../utils/logger.js';
import { mcpHandler } from '../mcp/mcpHandler.js';
import { visionHandler } from './visionHandler.js';

/**
 * Cursor CLI Provider
 *
 * Uses the Cursor CLI (cursor-agent) to interact with Cursor's AI models.
 * Supports both interactive and non-interactive modes.
 */
export class CursorCLIProvider extends EventEmitter {
  constructor() {
    super();
    this.isInitialized = false;
    this.sessionId = null;
    this.cliProcess = null;
    this.defaultModel = process.env.CURSOR_DEFAULT_MODEL || 'cursor-small';
    this.conversationHistory = new Map(); // Track conversation history per session
    this.activeSessions = new Map(); // Track active interactive sessions
    this.initializationPromise = this.initialize();
  }

  async initialize() {
    try {
      // Check if cursor-agent is available
      await this.checkCursorCLI();
      this.isInitialized = true;
      logger.info('✅ Cursor CLI Provider initialized successfully');
    } catch (error) {
      logger.error('❌ Failed to initialize Cursor CLI Provider:', error);
      // Don't throw error, allow fallback to mock responses
      this.isInitialized = false;
    }
  }

  async checkCursorCLI() {
    return new Promise((resolve, reject) => {
      logger.debug('Checking Cursor CLI installation...');

      const checkProcess = spawn('cursor-agent', ['--version'], {
        stdio: 'pipe',
        env: { ...process.env, PATH: `${process.env.HOME}/.local/bin:${process.env.PATH}` }
      });

      let output = '';
      checkProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      checkProcess.on('close', (code) => {
        if (code === 0) {
          logger.debug(`Cursor CLI found, version: ${output.trim()}`);
          resolve();
        } else {
          reject(new Error('Cursor CLI not found. Please install it first: curl https://cursor.com/install -fsS | bash'));
        }
      });

      checkProcess.on('error', (error) => {
        reject(new Error(`Failed to check Cursor CLI: ${error.message}`));
      });
    });
  }

  async getStatus() {
    return {
      initialized: this.isInitialized,
      defaultModel: this.defaultModel,
      authMethod: 'Cursor Account',
      contextWindow: 200000,
      capabilities: [
        'Text generation',
        'Code completion',
        'Code review',
        'Debugging assistance',
        'Interactive sessions',
        'Non-interactive mode'
      ],
      supportedFeatures: [
        'streaming',
        'non-streaming',
        'session management',
        'model selection',
        'print mode'
      ],
      models: await this.getModels()
    };
  }

  async getModels() {
    return [
      // Premium Models
      {
        id: 'cursor/gpt-5',
        name: 'GPT-5',
        provider: 'cursor',
        contextWindow: 200000,
        capabilities: ['text', 'code', 'reasoning', 'multimodal'],
        originalId: 'gpt-5',
        description: 'GPT-5 via Cursor - Most advanced OpenAI model',
        category: 'openai',
        vision: true,
        premium: true
      },
      {
        id: 'cursor/sonnet-4',
        name: 'Claude Sonnet 4',
        provider: 'cursor',
        contextWindow: 200000,
        capabilities: ['text', 'code', 'reasoning', 'multimodal'],
        originalId: 'sonnet-4',
        description: 'Claude Sonnet 4 via Cursor - Most advanced Anthropic model',
        category: 'anthropic',
        vision: true,
        premium: true
      }
    ];
  }

  // Alias for compatibility with ProviderManager
  async getAvailableModels() {
    return await this.getModels();
  }

  // Convert prefixed model ID to original Cursor CLI model ID
  getOriginalModelId(modelId) {
    // Remove cursor/ prefix if present
    if (modelId.startsWith('cursor/')) {
      return modelId.replace('cursor/', '');
    }
    return modelId;
  }

  // Execute Cursor CLI command with session management
  async executeCursorCommand(prompt, options = {}) {
    // Wait for initialization to complete
    await this.initializationPromise;

    if (!this.isInitialized) {
      logger.warn('Cursor CLI not initialized, falling back to mock response');
      return this.generateMockResponse();
    }

    const sessionId = options.session_id || 'default';

    try {
      logger.info(`Executing Cursor CLI command for session: ${sessionId}`);

      // Try to use interactive session first
      if (this.activeSessions.has(sessionId)) {
        return await this.sendToInteractiveSession(sessionId, prompt, options);
      } else {
        // Create new interactive session
        return await this.createInteractiveSession(sessionId, prompt, options);
      }
    } catch (error) {
      logger.error(`Cursor CLI execution failed: ${error.message}`);

      // Clean up failed session
      this.cleanupSession(sessionId);

      // Fallback to one-shot command
      try {
        logger.warn('Falling back to one-shot Cursor CLI command');
        return await this.tryRealCursorCLI(prompt, options);
      } catch (fallbackError) {
        logger.error(`Fallback also failed: ${fallbackError.message}`);

        // Final fallback to mock response
        if (error.message.includes('not found') || error.message.includes('ENOENT')) {
          logger.warn('Cursor CLI not available, using mock response');
          return this.generateMockResponse();
        }
        throw error;
      }
    }
  }

  async tryRealCursorCLI(prompt, options = {}) {
    return new Promise((resolve, reject) => {
      logger.info(`Executing Cursor CLI with prompt: ${prompt.substring(0, 50)}...`);

      // Build command args based on claudecodeui implementation
      const args = [];

      // Add prompt
      if (prompt && prompt.trim()) {
        args.push('-p', prompt);
      }

      // Add model if specified
      if (options.model) {
        const originalModel = this.getOriginalModelId(options.model);
        args.push('--model', originalModel);
      }

      // Request streaming JSON output
      args.push('--output-format', 'stream-json');

      logger.info(`Cursor CLI command: cursor-agent ${args.join(' ')}`);

      const cursorProcess = spawn('cursor-agent', args, {
        stdio: 'pipe',
        env: { ...process.env, PATH: `${process.env.HOME}/.local/bin:${process.env.PATH}` }
      });

      let output = '';
      let errorOutput = '';
      let hasReceivedOutput = false;
      let isResolved = false;
      let messageBuffer = '';

      // Increase timeout to 60 seconds for better reliability
      const timeout = setTimeout(() => {
        if (!isResolved) {
          cursorProcess.kill('SIGTERM');
          reject(new Error('Cursor CLI command timed out after 60 seconds'));
        }
      }, 60000);

      cursorProcess.stdout.on('data', (data) => {
        const dataStr = data.toString();
        output += dataStr;
        hasReceivedOutput = true;
        logger.debug(`Cursor CLI stdout chunk: ${dataStr.substring(0, 200)}...`);

        // Process streaming JSON responses
        const lines = dataStr.split('\n').filter(line => line.trim());

        for (const line of lines) {
          try {
            const response = JSON.parse(line);
            logger.debug('Parsed JSON response:', response);

            // Handle different message types based on claudecodeui implementation
            switch (response.type) {
              case 'assistant':
                // Accumulate assistant message chunks
                if (response.message && response.message.content && response.message.content.length > 0) {
                  const textContent = response.message.content[0].text;
                  messageBuffer += textContent;
                }
                break;

              case 'result':
                // Session complete - resolve with accumulated message
                if (!isResolved) {
                  clearTimeout(timeout);
                  isResolved = true;

                  // Use accumulated message buffer or result content
                  const finalResponse = messageBuffer || response.result || 'Response completed successfully';

                  // Don't kill immediately, let it finish naturally
                  setTimeout(() => {
                    if (!cursorProcess.killed) {
                      cursorProcess.kill('SIGTERM');
                    }
                  }, 100);

                  resolve(finalResponse);
                }
                break;
            }
          } catch (parseError) {
            // Skip non-JSON lines
            logger.debug('Non-JSON response line:', line);
          }
        }
      });

      cursorProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        logger.debug(`Cursor CLI stderr: ${data.toString()}`);
      });

      cursorProcess.on('close', (code) => {
        if (!isResolved) {
          clearTimeout(timeout);
          logger.info(`Cursor CLI process closed with code: ${code}`);

          if (hasReceivedOutput && (messageBuffer || output.trim())) {
            isResolved = true;
            // Use message buffer first, then try to parse output, finally fallback
            const finalResponse = messageBuffer || this.parseCursorResponse(output.trim()) || 'Response completed';
            resolve(finalResponse);
          } else {
            isResolved = true;
            reject(new Error(`Cursor CLI failed with code ${code}: ${errorOutput || 'No output received'}`));
          }
        }
      });

      cursorProcess.on('error', (error) => {
        if (!isResolved) {
          clearTimeout(timeout);
          logger.error(`Cursor CLI process error: ${error.message}`);
          isResolved = true;
          reject(new Error(`Failed to execute Cursor CLI: ${error.message}`));
        }
      });

      // Close stdin since cursor-agent doesn't need interactive input
      cursorProcess.stdin.end();
    });
  }

  // Create new session using cursor-agent
  async createInteractiveSession(sessionId, prompt, options = {}) {
    logger.info(`Creating new session: ${sessionId}`);

    try {
      // Use the improved cursor CLI implementation
      const response = await this.tryRealCursorCLI(prompt, options);

      // Store session info for tracking
      this.activeSessions.set(sessionId, {
        chatId: null, // Will be set if we can extract from cursor-agent
        history: [prompt],
        lastActivity: Date.now(),
        lastResponse: response
      });

      // Initialize conversation history
      if (!this.conversationHistory.has(sessionId)) {
        this.conversationHistory.set(sessionId, []);
      }

      // Add to conversation history
      const history = this.conversationHistory.get(sessionId);
      history.push({ role: 'user', content: prompt });
      history.push({ role: 'assistant', content: response });
      this.conversationHistory.set(sessionId, history);

      return response;
    } catch (error) {
      logger.error(`Failed to create cursor session: ${error.message}`);
      // Clean up failed session
      this.activeSessions.delete(sessionId);
      throw error;
    }
  }

  // Send message to existing session (using conversation history)
  async sendToInteractiveSession(sessionId, prompt, options = {}) {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      // Session doesn't exist, create new one
      return await this.createInteractiveSession(sessionId, prompt, options);
    }

    logger.info(`Continuing conversation in session: ${sessionId}`);

    // Update last activity
    session.lastActivity = Date.now();
    session.history.push(prompt);

    // Get full conversation history for this session
    const history = this.conversationHistory.get(sessionId) || [];

    // Create full prompt with conversation context
    const fullPrompt = this.buildConversationPrompt(history, prompt);

    // Use one-shot command with full context (since cursor-agent doesn't have true interactive mode)
    const response = await this.tryRealCursorCLI(fullPrompt, options);

    // Add to conversation history
    history.push({ role: 'user', content: prompt });
    history.push({ role: 'assistant', content: response });
    this.conversationHistory.set(sessionId, history);

    // Update session
    session.lastResponse = response;

    return response;
  }

  // Build conversation prompt with history context
  buildConversationPrompt(history, newPrompt) {
    if (history.length === 0) {
      return newPrompt;
    }

    // Build conversation context
    let contextPrompt = "Previous conversation:\n";
    for (const msg of history) {
      if (msg.role === 'user') {
        contextPrompt += `User: ${msg.content}\n`;
      } else if (msg.role === 'assistant') {
        contextPrompt += `Assistant: ${msg.content}\n`;
      }
    }

    contextPrompt += `\nCurrent message:\nUser: ${newPrompt}\n\nPlease respond considering the conversation history above.`;

    return contextPrompt;
  }



  // Clean up session
  cleanupSession(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (session) {
      // For cursor-agent, we don't have persistent processes to kill
      // Just clean up the session data
      logger.info(`Cleaning up session data for: ${sessionId}`);
    }
    this.activeSessions.delete(sessionId);
    // Keep conversation history for potential resume
    // this.conversationHistory.delete(sessionId); // Commented out to keep history
    logger.info(`Cleaned up session: ${sessionId}`);
  }

  parseCursorResponse(rawOutput) {
    try {
      // Cursor CLI returns JSON lines, we need to extract the actual response
      const lines = rawOutput.split('\n').filter(line => line.trim());

      // First, look for the final result field which contains the complete response
      for (const line of lines) {
        try {
          const parsed = JSON.parse(line);

          // Look for the final result field (new format)
          if (parsed.type === 'result' && parsed.result && typeof parsed.result === 'string') {
            logger.info('Found final result from Cursor CLI');
            return parsed.result;
          }
        } catch (parseError) {
          // Skip invalid JSON lines
          continue;
        }
      }

      // Fallback: Try to reconstruct from streaming assistant messages
      let reconstructedResponse = '';
      for (const line of lines) {
        try {
          const parsed = JSON.parse(line);

          // Look for assistant streaming messages (claudecodeui format)
          if (parsed.type === 'assistant' && parsed.message && parsed.message.content) {
            if (Array.isArray(parsed.message.content)) {
              // Extract text from content array
              const textContent = parsed.message.content
                .filter(item => item.type === 'text' || !item.type)
                .map(item => item.text || item)
                .join('');
              reconstructedResponse += textContent;
            } else if (typeof parsed.message.content === 'string') {
              reconstructedResponse += parsed.message.content;
            }
          }

          // Look for other response formats
          if (parsed.content && typeof parsed.content === 'string') {
            reconstructedResponse += parsed.content;
          }

          // Look for text field directly
          if (parsed.text && typeof parsed.text === 'string') {
            reconstructedResponse += parsed.text;
          }
        } catch (parseError) {
          // Skip invalid JSON lines
          continue;
        }
      }

      // If we reconstructed something, return it
      if (reconstructedResponse.trim()) {
        logger.info('Reconstructed response from streaming chunks');
        return reconstructedResponse.trim();
      }

      // If no structured response found, return a default message
      logger.warn('Could not parse Cursor CLI response, using fallback');
      return 'Hello! I\'m a premium AI model (GPT-5/Sonnet-4) via Cursor. How can I help you?';

    } catch (error) {
      logger.error('Error parsing Cursor CLI response:', error);
      return this.generateMockResponse();
    }
  }

  generateMockResponse() {
    // Generate a mock response
    const responses = [
      "I'm a premium AI model (GPT-5/Sonnet-4) via Cursor. I can help you with advanced coding, reasoning, and complex tasks.",
      "Hello! I'm running through Cursor's premium models. How can I assist you with your coding or analysis needs?",
      "I'm powered by Cursor's most advanced models. I can help with sophisticated programming, debugging, and problem-solving tasks.",
      "Hi there! I'm using Cursor's premium AI capabilities. What would you like to work on together?"
    ];

    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    logger.info('Generated mock response for Cursor CLI');
    return randomResponse;
  }

  // Convert messages to prompt with session context
  convertMessagesToPrompt(messages, sessionId = 'default') {
    // Get conversation history for this session
    const history = this.conversationHistory.get(sessionId) || [];

    // Combine history with current messages
    const allMessages = [...history, ...messages];

    return allMessages.map(msg => {
      if (msg.role === 'user') {
        return msg.content;
      } else if (msg.role === 'assistant') {
        return `Assistant: ${msg.content}`;
      } else if (msg.role === 'system') {
        return `System: ${msg.content}`;
      }
      return msg.content;
    }).join('\n\n');
  }

  // Estimate tokens (rough approximation)
  estimateTokens(text) {
    return Math.ceil(text.length / 4);
  }

  // Create completion (non-streaming)
  async createCompletion(messages, options = {}) {
    try {
      const sessionId = options.session_id || 'default';
      const prompt = this.convertMessagesToPrompt(messages, sessionId);
      const response = await this.executeCursorCommand(prompt, options);

      // Add response to conversation history
      const history = this.conversationHistory.get(sessionId) || [];
      history.push({ role: 'assistant', content: response });
      this.conversationHistory.set(sessionId, history);

      return {
        id: `cursor-${Date.now()}`,
        object: 'chat.completion',
        created: Math.floor(Date.now() / 1000),
        model: options.model || this.defaultModel,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response
          },
          finish_reason: 'stop'
        }],
        usage: {
          prompt_tokens: this.estimateTokens(prompt),
          completion_tokens: this.estimateTokens(response),
          total_tokens: this.estimateTokens(prompt + response)
        }
      };
    } catch (error) {
      logger.error('Error in Cursor createCompletion:', error);
      throw error;
    }
  }

  // Stream completion
  async streamCompletion(messages, options = {}, onChunk) {
    try {
      // For now, use non-streaming and simulate streaming
      const result = await this.createCompletion(messages, options);
      const content = result.choices[0].message.content;

      // Simulate streaming by sending chunks
      const words = content.split(' ');
      for (let i = 0; i < words.length; i++) {
        const chunk = {
          id: result.id,
          object: 'chat.completion.chunk',
          created: result.created,
          model: result.model,
          choices: [{
            index: 0,
            delta: {
              content: (i === 0 ? '' : ' ') + words[i]
            },
            finish_reason: i === words.length - 1 ? 'stop' : null
          }]
        };

        if (onChunk) {
          onChunk(chunk);
        }

        // Small delay to simulate streaming
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      return result;
    } catch (error) {
      logger.error('Error in Cursor streamCompletion:', error);
      throw error;
    }
  }

  // Session management methods
  getActiveSessionCount() {
    return this.activeSessions.size;
  }

  getSessionInfo(sessionId) {
    const session = this.activeSessions.get(sessionId);
    if (!session) return null;

    const conversationHistory = this.conversationHistory.get(sessionId) || [];

    return {
      sessionId,
      historyLength: session.history.length,
      conversationLength: conversationHistory.length,
      lastActivity: session.lastActivity,
      lastResponse: session.lastResponse ? session.lastResponse.substring(0, 100) + '...' : null,
      isActive: true // For cursor-agent, sessions are conceptually always active
    };
  }

  getAllSessions() {
    const sessions = [];
    for (const [sessionId] of this.activeSessions.entries()) {
      sessions.push(this.getSessionInfo(sessionId));
    }
    return sessions;
  }

  // Clean up old inactive sessions
  cleanupInactiveSessions(maxAge = 30 * 60 * 1000) { // 30 minutes default
    const now = Date.now();
    const sessionsToCleanup = [];

    for (const [sessionId, session] of this.activeSessions.entries()) {
      if (now - session.lastActivity > maxAge) {
        sessionsToCleanup.push(sessionId);
      }
    }

    for (const sessionId of sessionsToCleanup) {
      logger.info(`Cleaning up inactive session: ${sessionId}`);
      this.cleanupSession(sessionId);
    }

    return sessionsToCleanup.length;
  }

  // Cleanup all sessions (for shutdown)
  cleanupAllSessions() {
    const sessionIds = Array.from(this.activeSessions.keys());
    logger.info(`Cleaning up ${sessionIds.length} cursor sessions`);

    for (const sessionId of sessionIds) {
      this.cleanupSession(sessionId);
    }

    // Clear conversation history on shutdown
    this.conversationHistory.clear();
    logger.info('All cursor sessions cleaned up');
  }
}