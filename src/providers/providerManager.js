import { GeminiCLIProvider } from './geminiProvider.js';
import { Claude<PERSON>IProvider } from './claudeProvider.js';
import { QwenCLIProvider } from './qwenProvider.js';
import { QwenCodeCLIProvider } from './qwenCodeCliProvider.js';
import { CursorCLIProvider } from './cursorProvider.js';
import { logger } from '../utils/logger.js';

export class ProviderManager {
  constructor() {
    this.providers = new Map();
    this.modelToProvider = new Map();
    this.initialize();
  }

  async initialize() {
    try {
      // Initialize Gemini provider
      const geminiProvider = new GeminiCLIProvider();
      this.providers.set('gemini', geminiProvider);
      
      // Initialize Claude provider
      const claudeProvider = new ClaudeCLIProvider();
      this.providers.set('claude', claudeProvider);
      
      // Initialize Qwen providers
      const qwenProvider = new QwenCLIProvider(); // OpenAI-compatible
      this.providers.set('qwen', qwenProvider);
      const qwenCliProvider = new QwenCodeCLIProvider(); // Qwen Code CLI OAuth
      this.providers.set('qwen-cli', qwenCliProvider);

      // Initialize Cursor provider
      const cursorProvider = new CursorCLIProvider();
      this.providers.set('cursor', cursorProvider);
      
      // Build model to provider mapping
      await this.buildModelMapping();
      
      logger.info('✅ Provider Manager initialized with Gemini, Claude, Qwen (OpenAI), Qwen Code CLI and Cursor providers');
    } catch (error) {
      logger.error('❌ Failed to initialize Provider Manager:', error);
      throw error;
    }
  }

  async buildModelMapping() {
    // Clear existing mapping
    this.modelToProvider.clear();
    
    // Map Gemini models
    if (this.providers.has('gemini')) {
      const geminiModels = await this.providers.get('gemini').getAvailableModels();
      for (const model of geminiModels) {
        this.modelToProvider.set(model.id, 'gemini');
      }
    }
    
    // Map Claude models
    if (this.providers.has('claude')) {
      const claudeModels = await this.providers.get('claude').getAvailableModels();
      for (const model of claudeModels) {
        this.modelToProvider.set(model.id, 'claude');
      }
    }
    
    // Map Qwen models
    if (this.providers.has('qwen')) {
      const qwenModels = await this.providers.get('qwen').getAvailableModels();
      for (const model of qwenModels) {
        this.modelToProvider.set(model.id, 'qwen');
      }
    }
    
    // Map Qwen Code CLI models
    if (this.providers.has('qwen-cli')) {
      const qwenCliModels = await this.providers.get('qwen-cli').getAvailableModels();
      for (const model of qwenCliModels) {
        this.modelToProvider.set(model.id, 'qwen-cli');
      }
    }

    // Map Cursor models
    if (this.providers.has('cursor')) {
      const cursorModels = await this.providers.get('cursor').getAvailableModels();
      for (const model of cursorModels) {
        this.modelToProvider.set(model.id, 'cursor');
      }
    }

    logger.debug('Model to provider mapping built:', Object.fromEntries(this.modelToProvider));
  }

  getProviderForModel(modelId) {
    logger.debug(`Looking for provider for model: ${modelId}`);
    logger.debug(`Available model mappings:`, Object.fromEntries(this.modelToProvider));
    
    const providerName = this.modelToProvider.get(modelId);
    if (!providerName) {
      // If model name contains 'claude', assume it's for Claude provider
      if (modelId.includes('claude')) {
        logger.warn(`Model ${modelId} not in mapping, but name suggests Claude. Routing to Claude provider.`);
        return this.providers.get('claude');
      }
      // If model name contains 'gemini', assume it's for Gemini provider
      if (modelId.includes('gemini')) {
        logger.warn(`Model ${modelId} not in mapping, but name suggests Gemini. Routing to Gemini provider.`);
        return this.providers.get('gemini');
      }
      // If model name contains 'qwen', assume it's for Qwen providers
      if (modelId.toLowerCase().includes('qwen')) {
        logger.warn(`Model ${modelId} not in mapping, but name suggests Qwen. Routing to Qwen provider.`);
        // Prefer CLI provider when available for OAuth-based setups
        return this.providers.get('qwen-cli') || this.providers.get('qwen');
      }
      // If model is GPT-5 or Sonnet-4, route to Cursor provider
      if (modelId.includes('gpt-5') || modelId.includes('sonnet-4') || modelId.startsWith('cursor/')) {
        logger.warn(`Model ${modelId} not in mapping, but is premium model. Routing to Cursor provider.`);
        return this.providers.get('cursor');
      }
      // Otherwise, throw an error to trigger fallback
      throw new Error(`Model ${modelId} not found in any provider and could not be inferred.`);
    }
    
    logger.info(`✅ Routing model ${modelId} to ${providerName} provider`);
    
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found for model ${modelId}`);
    }
    
    return provider;
  }

  async getAvailableModels() {
    const allModels = [];
    
    // Get models from all providers
    for (const [providerName, provider] of this.providers) {
      try {
        const models = await provider.getAvailableModels();
        allModels.push(...models);
      } catch (error) {
        logger.error(`Failed to get models from ${providerName} provider:`, error);
      }
    }
    
    return allModels;
  }

  async getStatus() {
    const status = {
      providers: {},
      totalModels: 0,
      activeProviders: 0
    };
    
    // Get status from all providers
    for (const [providerName, provider] of this.providers) {
      try {
        const providerStatus = await provider.getStatus();
        status.providers[providerName] = providerStatus;
        
        if (providerStatus.initialized) {
          status.activeProviders++;
        }
      } catch (error) {
        logger.error(`Failed to get status from ${providerName} provider:`, error);
        status.providers[providerName] = {
          initialized: false,
          error: error.message
        };
      }
    }
    
    // Count total models
    try {
      const models = await this.getAvailableModels();
      status.totalModels = models.length;
    } catch (error) {
      logger.error('Failed to count total models:', error);
    }
    
    return status;
  }

  async createCompletion(messages, options = {}) {
    const modelId = options.model || 'gemini-2.5-flash';
    logger.info(`🔍 createCompletion called with model: ${modelId}`);
    logger.debug(`Full options:`, options);
    
    const provider = this.getProviderForModel(modelId);
    
    logger.debug(`Routing completion request for model ${modelId} to ${this.getProviderName(provider)} provider`);
    
    try {
      return await provider.createCompletion(messages, options);
    } catch (error) {
      logger.error(`Failed to create completion with ${this.getProviderName(provider)} provider:`, error);
      
      // Try fallback to other provider if available
      const fallbackProvider = this.getFallbackProvider(provider);
      if (fallbackProvider && options.allowFallback !== false) {
        logger.warn(`Attempting fallback to ${this.getProviderName(fallbackProvider)} provider`);
        
        // Update model to the default for the fallback provider
        const fallbackProviderName = this.getProviderName(fallbackProvider);
        const fallbackModelId = fallbackProviderName === 'claude' ? 'claude-4-sonnet' : 'gemini-2.5-flash';
        
        const fallbackOptions = { ...options, model: fallbackModelId };
        return await fallbackProvider.createCompletion(messages, fallbackOptions);
      }
      
      throw error;
    }
  }

  async streamCompletion(messages, options = {}, onChunk) {
    const modelId = options.model || 'gemini-2.5-flash';
    const provider = this.getProviderForModel(modelId);
    
    logger.debug(`Routing streaming completion request for model ${modelId} to ${this.getProviderName(provider)} provider`);
    
    try {
      return await provider.streamCompletion(messages, options, onChunk);
    } catch (error) {
      logger.error(`Failed to stream completion with ${this.getProviderName(provider)} provider:`, error);
      
      // Try fallback to other provider if available
      const fallbackProvider = this.getFallbackProvider(provider);
      if (fallbackProvider && options.allowFallback !== false) {
        logger.warn(`Attempting streaming fallback to ${this.getProviderName(fallbackProvider)} provider`);
        
        // Update model to the default for the fallback provider
        const fallbackProviderName = this.getProviderName(fallbackProvider);
        const fallbackModelId = fallbackProviderName === 'claude' ? 'claude-4-sonnet' : 'gemini-2.5-flash';
        
        const fallbackOptions = { ...options, model: fallbackModelId };
        return await fallbackProvider.streamCompletion(messages, fallbackOptions, onChunk);
      }
      
      throw error;
    }
  }

  getProviderName(provider) {
    for (const [name, p] of this.providers) {
      if (p === provider) {
        return name;
      }
    }
    return 'unknown';
  }

  getFallbackProvider(currentProvider) {
    // Simple fallback logic: if current is gemini, try claude, and vice versa
    const currentName = this.getProviderName(currentProvider);
    
    if (currentName === 'gemini') {
      return this.providers.get('claude');
    } else if (currentName === 'claude') {
      return this.providers.get('gemini');
    }
    
    return null;
  }

  // Model management methods
  async addCustomModel(model, providerName = 'gemini') {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }
    
    const result = await provider.addCustomModel(model);
    
    // Rebuild model mapping
    await this.buildModelMapping();
    
    return result;
  }

  async deleteModel(modelId) {
    const providerName = this.modelToProvider.get(modelId);
    if (!providerName) {
      throw new Error(`Model ${modelId} not found`);
    }
    
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }
    
    const result = await provider.deleteModel(modelId);
    
    // Rebuild model mapping
    await this.buildModelMapping();
    
    return result;
  }

  async resetModels(providerName = null) {
    if (providerName) {
      // Reset specific provider
      const provider = this.providers.get(providerName);
      if (!provider) {
        throw new Error(`Provider ${providerName} not found`);
      }
      
      await provider.resetModels();
    } else {
      // Reset all providers
      for (const [name, provider] of this.providers) {
        try {
          await provider.resetModels();
        } catch (error) {
          logger.error(`Failed to reset models for ${name} provider:`, error);
        }
      }
    }
    
    // Rebuild model mapping
    await this.buildModelMapping();
    
    return { success: true };
  }

  // API Key management
  async getApiKeys(providerName = null) {
    const keys = {};
    
    if (providerName) {
      const provider = this.providers.get(providerName);
      if (provider && typeof provider.getApiKeys === 'function') {
        keys[providerName] = await provider.getApiKeys();
      }
    } else {
      // Get keys from all providers
      for (const [name, provider] of this.providers) {
        if (typeof provider.getApiKeys === 'function') {
          try {
            keys[name] = await provider.getApiKeys();
          } catch (error) {
            logger.error(`Failed to get API keys from ${name} provider:`, error);
            keys[name] = [];
          }
        }
      }
    }
    
    return keys;
  }

  async addApiKey(key, name, providerName) {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }
    
    if (typeof provider.addApiKey !== 'function') {
      throw new Error(`Provider ${providerName} does not support API key management`);
    }
    
    return await provider.addApiKey(key, name);
  }

  async deleteApiKey(keyId, providerName) {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }
    
    if (typeof provider.deleteApiKey !== 'function') {
      throw new Error(`Provider ${providerName} does not support API key management`);
    }
    
    return await provider.deleteApiKey(keyId);
  }

  // Settings management
  async updateSettings(settings, providerName = null) {
    if (providerName) {
      const provider = this.providers.get(providerName);
      if (!provider) {
        throw new Error(`Provider ${providerName} not found`);
      }
      
      if (typeof provider.updateSettings === 'function') {
        return await provider.updateSettings(settings);
      } else {
        throw new Error(`Provider ${providerName} does not support settings management`);
      }
    } else {
      // Update settings for all providers that support it
      const results = {};
      
      for (const [name, provider] of this.providers) {
        if (typeof provider.updateSettings === 'function') {
          try {
            results[name] = await provider.updateSettings(settings);
          } catch (error) {
            logger.error(`Failed to update settings for ${name} provider:`, error);
            results[name] = { error: error.message };
          }
        }
      }
      
      return results;
    }
  }

  async destroy() {
    // Destroy all providers
    for (const [name, provider] of this.providers) {
      try {
        if (typeof provider.destroy === 'function') {
          await provider.destroy();
        }
      } catch (error) {
        logger.error(`Failed to destroy ${name} provider:`, error);
      }
    }
    
    this.providers.clear();
    this.modelToProvider.clear();
    
    logger.info('Provider Manager destroyed');
  }
}

// Create singleton instance
export const providerManager = new ProviderManager();