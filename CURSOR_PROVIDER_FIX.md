# Cursor Provider Conversation Continuity Fix

## Problem Description

The Cursor provider in Open WebUI was experiencing an issue where it would only respond to the first message in a conversation, then stop responding to subsequent messages. This was causing frustration for users trying to have multi-turn conversations.

## Root Cause Analysis

After analyzing the code, the issue was identified in the `cursorProvider.js` implementation:

1. **No Session Management**: Each request created a completely new process without maintaining conversation state
2. **One-Shot Commands**: The provider used one-shot `cursor-agent` commands instead of interactive sessions
3. **No Conversation History**: Previous messages were not being maintained between requests
4. **Process Cleanup Issues**: Processes were being killed immediately after each response, preventing session continuity

## Solution Implementation

### 1. Added Session Management

```javascript
// Added to constructor
this.conversationHistory = new Map(); // Track conversation history per session
this.activeSessions = new Map(); // Track active interactive sessions
```

### 2. Interactive Session Support

- **`createInteractiveSession()`**: Creates persistent interactive sessions with cursor-agent
- **`sendToInteractiveSession()`**: Sends messages to existing sessions
- **`sendPromptToSession()`**: Handles communication with session processes

### 3. Conversation History Tracking

```javascript
// Enhanced convertMessagesToPrompt to include session context
convertMessagesToPrompt(messages, sessionId = 'default') {
  const history = this.conversationHistory.get(sessionId) || [];
  const allMessages = [...history, ...messages];
  // ... format messages with context
}
```

### 4. Session Lifecycle Management

- **Session Creation**: Automatic creation of interactive sessions when needed
- **Session Cleanup**: Automatic cleanup of inactive sessions
- **Graceful Shutdown**: Proper cleanup on server shutdown
- **Error Recovery**: Fallback to one-shot commands if interactive sessions fail

### 5. Enhanced API Integration

```javascript
// Extract session_id from multiple sources
const sessionId = req.body.session_id || 
                 req.headers['x-session-id'] || 
                 req.headers['x-chat-id'] ||
                 process.env.OPENWEBUI_CURRENT_SESSION_ID ||
                 'default';
```

## Key Features Added

### Session Management
- ✅ Persistent interactive sessions
- ✅ Conversation history tracking per session
- ✅ Automatic session cleanup
- ✅ Session info debugging endpoints

### Error Handling
- ✅ Fallback to one-shot commands if interactive sessions fail
- ✅ Graceful error recovery
- ✅ Proper process cleanup
- ✅ Timeout handling

### Debugging & Monitoring
- ✅ `/v1/debug/cursor-sessions` - View active sessions
- ✅ `/v1/debug/cursor-cleanup` - Manual session cleanup
- ✅ Session activity tracking
- ✅ Enhanced logging

## Testing

### Automated Test Script

Run the test script to verify the fix:

```bash
node test-cursor-conversation.js
```

The test script:
1. Sends multiple messages in sequence
2. Tests conversation continuity
3. Verifies session management
4. Provides detailed results

### Manual Testing

1. Start a conversation with cursor provider
2. Send multiple messages
3. Verify each response references previous context
4. Check session info via debug endpoints

## API Changes

### New Session Management

```javascript
// Requests now support session_id
{
  "model": "cursor/gpt-5",
  "messages": [...],
  "session_id": "my-session-123"  // Optional, defaults to 'default'
}
```

### Debug Endpoints

```bash
# View active sessions
GET /v1/debug/cursor-sessions

# Manual cleanup
POST /v1/debug/cursor-cleanup
{
  "maxAge": 1800000  // Optional: max age in ms (default: 30 min)
}
```

## Configuration

### Environment Variables

- `OPENWEBUI_CURRENT_SESSION_ID`: Current session ID from Open WebUI
- `CURSOR_DEFAULT_MODEL`: Default cursor model to use

### Session Headers

- `x-session-id`: Session identifier
- `x-chat-id`: Chat identifier (alternative to session-id)

## Performance Considerations

### Memory Management
- Sessions are automatically cleaned up after 30 minutes of inactivity
- Conversation history is limited per session
- Process cleanup on server shutdown

### Resource Usage
- Interactive sessions use more memory than one-shot commands
- Each session maintains a persistent cursor-agent process
- Automatic cleanup prevents resource leaks

## Backward Compatibility

- ✅ Existing API calls continue to work
- ✅ Falls back to one-shot commands if interactive sessions fail
- ✅ Default session handling for requests without session_id
- ✅ Mock responses when cursor-agent is not available

## Troubleshooting

### Common Issues

1. **Sessions not persisting**
   - Check if cursor-agent supports interactive mode
   - Verify session_id is being passed correctly
   - Check debug endpoints for session status

2. **High memory usage**
   - Monitor active session count
   - Adjust cleanup intervals if needed
   - Use manual cleanup endpoint if necessary

3. **Process cleanup issues**
   - Check server logs for cleanup messages
   - Verify graceful shutdown handling
   - Monitor for zombie processes

### Debug Commands

```bash
# Check session status
curl -H "Authorization: Bearer $API_KEY" \
  http://localhost:8010/v1/debug/cursor-sessions

# Manual cleanup
curl -X POST -H "Authorization: Bearer $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"maxAge": 600000}' \
  http://localhost:8010/v1/debug/cursor-cleanup
```

## Future Improvements

1. **Session Persistence**: Save sessions to disk for server restarts
2. **Load Balancing**: Distribute sessions across multiple cursor-agent instances
3. **Advanced Cleanup**: More sophisticated session lifecycle management
4. **Metrics**: Detailed session usage metrics and monitoring

## Conclusion

This fix resolves the conversation continuity issue by implementing proper session management, conversation history tracking, and robust error handling. The cursor provider now maintains context across multiple messages, providing a seamless chat experience in Open WebUI.
