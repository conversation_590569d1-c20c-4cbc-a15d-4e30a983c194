# 🚀 Service Management Guide

Hướng dẫn quản lý các services trong Gemini CLI Wrapper project.

## 📋 Services Overview

| Service | Port | Description | Status |
|---------|------|-------------|--------|
| **Gemini CLI Wrapper** | 8010 | Main API server | ✅ Auto-restart |
| **MCP Search Engine** | 7860 | Search engine using Gemini | ✅ Auto-restart |
| **MCPO Server** | 8000 | External MCP server | ⚠️ External |

## 🛠️ Management Scripts

### Quick Commands

```bash
# Khởi động nhanh tất cả services
./quick_start_services.sh

# Test tất cả services
./test_all_services.sh

# Khởi động với auto-start setup
./auto_start.sh
```

### PM2 Commands

```bash
# Xem trạng thái
pm2 status

# Khởi động tất cả
pm2 start ecosystem.config.cjs

# Restart tất cả
pm2 restart all

# Dừng tất cả
pm2 stop all

# Xóa tất cả
pm2 delete all

# Xem logs
pm2 logs
pm2 logs gemini-cli-wrapper
pm2 logs mcp-search-engine

# Lưu cấu hình
pm2 save
```

## 🔧 Setup Auto-Start

### 1. Setup PM2 với Systemd

```bash
# Chạy script setup
./setup_pm2_autostart.sh

# Sau đó chạy lệnh được hiển thị (với sudo)
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u ubuntu --hp /home/<USER>

# Lưu cấu hình
pm2 save
```

### 2. Kiểm tra Auto-Start

```bash
# Restart server để test
sudo reboot

# Sau khi server khởi động lại, kiểm tra
pm2 status
./test_all_services.sh
```

## 🐛 Troubleshooting

### MCP Search Engine không phản hồi

```bash
# Kiểm tra logs
pm2 logs mcp-search-engine

# Restart service
pm2 restart mcp-search-engine

# Kiểm tra port
netstat -tlnp | grep 7860
```

### Gemini CLI Wrapper lỗi

```bash
# Kiểm tra logs
pm2 logs gemini-cli-wrapper

# Restart service
pm2 restart gemini-cli-wrapper

# Test health endpoint
curl http://localhost:8010/health
```

### Services không tự khởi động

```bash
# Kiểm tra systemd service
systemctl status pm2-ubuntu

# Khởi động lại PM2 systemd service
sudo systemctl restart pm2-ubuntu

# Kiểm tra PM2 startup
pm2 startup
```

## 📊 Monitoring

### Real-time Monitoring

```bash
# Monitor tất cả processes
pm2 monit

# Xem logs real-time
pm2 logs --lines 50

# Xem metrics
pm2 show gemini-cli-wrapper
pm2 show mcp-search-engine
```

### Health Checks

```bash
# Quick health check
curl http://localhost:8010/health
curl http://localhost:7860 -I

# Full test
./test_all_services.sh
```

## 🔄 Update Process

### Cập nhật code

```bash
# Pull latest code
git pull

# Restart services
pm2 restart all

# Verify
./test_all_services.sh
```

### Cập nhật dependencies

```bash
# Update main project
npm install

# Update MCP Search Engine
cd mcp_search_engine
npm install
cd ..

# Restart services
pm2 restart all
```

## 📝 Configuration Files

- `ecosystem.config.cjs` - PM2 configuration
- `mcp_search_engine/index.js` - MCP Search Engine server
- `src/index.js` - Main Gemini CLI Wrapper server

## 🌐 Access URLs

- **Gemini CLI Wrapper**: http://localhost:8010
- **MCP Search Engine**: http://localhost:7860
- **Health Check**: http://localhost:8010/health
- **Models List**: http://localhost:8010/v1/models

## 🚨 Emergency Commands

```bash
# Kill all Node.js processes (EMERGENCY ONLY)
pkill -f node

# Restart PM2 completely
pm2 kill
pm2 resurrect

# Force restart all services
pm2 delete all
pm2 start ecosystem.config.cjs
pm2 save
```

---

💡 **Tip**: Luôn sử dụng `./quick_start_services.sh` để khởi động nhanh và `./test_all_services.sh` để kiểm tra trạng thái!
