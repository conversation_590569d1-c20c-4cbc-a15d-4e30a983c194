#!/usr/bin/env node

/**
 * Test script to verify cursor provider conversation continuity
 * This script tests multi-turn conversations to ensure the fix works
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8010';
const API_KEY = process.env.API_KEY || 'test-token';

// Test configuration
const TEST_CONFIG = {
  model: 'cursor/gpt-5',
  session_id: `test-session-${Date.now()}`,
  messages: [
    "Hello, I'm testing conversation continuity. Please remember my name is <PERSON>.",
    "What's my name?",
    "Can you tell me a joke about programming?",
    "What was the topic of your previous joke?",
    "Let's switch topics. What's 2+2?",
    "What was the result of the math problem I just asked?"
  ]
};

async function sendMessage(message, sessionId, conversationHistory = []) {
  try {
    const messages = [
      ...conversationHistory,
      { role: 'user', content: message }
    ];

    console.log(`\n🔵 Sending: "${message}"`);
    console.log(`📋 Session ID: ${sessionId}`);
    console.log(`📚 History length: ${conversationHistory.length} messages`);

    const response = await axios.post(`${BASE_URL}/v1/chat/completions`, {
      model: TEST_CONFIG.model,
      messages: messages,
      session_id: sessionId,
      stream: false
    }, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });

    const reply = response.data.choices[0].message.content;
    console.log(`🟢 Response: "${reply}"`);
    
    return {
      success: true,
      message: reply,
      conversationHistory: [
        ...conversationHistory,
        { role: 'user', content: message },
        { role: 'assistant', content: reply }
      ]
    };
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data:`, error.response.data);
    }
    return {
      success: false,
      error: error.message,
      conversationHistory: conversationHistory
    };
  }
}

async function testConversationContinuity() {
  console.log('🚀 Starting Cursor Provider Conversation Continuity Test');
  console.log(`📝 Model: ${TEST_CONFIG.model}`);
  console.log(`🆔 Session ID: ${TEST_CONFIG.session_id}`);
  console.log(`📊 Test messages: ${TEST_CONFIG.messages.length}`);
  
  let conversationHistory = [];
  let successCount = 0;
  let failureCount = 0;

  for (let i = 0; i < TEST_CONFIG.messages.length; i++) {
    const message = TEST_CONFIG.messages[i];
    console.log(`\n--- Test ${i + 1}/${TEST_CONFIG.messages.length} ---`);
    
    const result = await sendMessage(message, TEST_CONFIG.session_id, conversationHistory);
    
    if (result.success) {
      successCount++;
      conversationHistory = result.conversationHistory;
      
      // Add delay between messages to simulate real conversation
      await new Promise(resolve => setTimeout(resolve, 2000));
    } else {
      failureCount++;
      console.error(`❌ Test ${i + 1} failed: ${result.error}`);
      // Continue with existing history even if one message fails
    }
  }

  // Test results
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`✅ Successful messages: ${successCount}/${TEST_CONFIG.messages.length}`);
  console.log(`❌ Failed messages: ${failureCount}/${TEST_CONFIG.messages.length}`);
  console.log(`📚 Final conversation length: ${conversationHistory.length} messages`);
  
  if (successCount === TEST_CONFIG.messages.length) {
    console.log('🎉 ALL TESTS PASSED! Conversation continuity is working.');
  } else if (successCount > 1) {
    console.log('⚠️  PARTIAL SUCCESS: Some messages worked, indicating session management is partially functional.');
  } else {
    console.log('💥 TESTS FAILED: Conversation continuity is not working properly.');
  }

  return {
    success: successCount === TEST_CONFIG.messages.length,
    successCount,
    failureCount,
    conversationHistory
  };
}

async function testSessionInfo() {
  console.log('\n🔍 Testing session info endpoint...');
  
  try {
    const response = await axios.get(`${BASE_URL}/v1/debug/cursor-sessions`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`
      }
    });

    console.log('📋 Session Info:');
    console.log(`Active sessions: ${response.data.activeSessionCount}`);
    console.log(`Sessions:`, response.data.sessions);
  } catch (error) {
    console.error('❌ Failed to get session info:', error.message);
  }
}

async function main() {
  console.log('🧪 Cursor Provider Multi-Turn Conversation Test');
  console.log('=' .repeat(60));
  
  try {
    // Test conversation continuity
    const result = await testConversationContinuity();
    
    // Test session info
    await testSessionInfo();
    
    // Final summary
    console.log('\n' + '='.repeat(60));
    console.log('🏁 TEST SUMMARY');
    console.log('='.repeat(60));
    
    if (result.success) {
      console.log('✅ Cursor provider conversation continuity is WORKING!');
      console.log('🎯 The fix has resolved the multi-turn conversation issue.');
    } else {
      console.log('❌ Cursor provider still has conversation continuity issues.');
      console.log('🔧 Additional debugging may be needed.');
    }
    
    process.exit(result.success ? 0 : 1);
    
  } catch (error) {
    console.error('💥 Test script failed:', error.message);
    process.exit(1);
  }
}

// Run the test
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { testConversationContinuity, sendMessage };
