import 'dotenv/config';
import express from 'express';
import axios from 'axios';
import { SearchCache } from './cache.js';

const app = express();
const port = 7860;

// The endpoint of the main Gemini CLI Wrapper
const GEMINI_WRAPPER_ENDPOINT = 'http://localhost:8010/v1/chat/completions';

// Initialize cache
const searchCache = new SearchCache({
  maxSize: 100,
  ttl: 5 * 60 * 1000 // 5 minutes
});

// Configure axios with timeout and keep-alive
const axiosInstance = axios.create({
  timeout: 180000, // 180 seconds timeout (3 minutes) for better reliability
  headers: {
    'Connection': 'keep-alive',
    'Keep-Alive': 'timeout=5, max=1000'
  }
});

app.use(express.json());

// Add cache stats endpoint
app.get('/cache/stats', (req, res) => {
  res.json(searchCache.getStats());
});

app.post('/search', async (req, res) => {
  const { query } = req.body;

  if (!query) {
    return res.status(400).json({ error: 'Query is required' });
  }

  console.log(`[MCP Search Engine] Received query: "${query}"`);

  // Check cache first
  const cachedResult = searchCache.get(query);
  if (cachedResult) {
    console.log(`[MCP Search Engine] Cache hit for query: "${query}"`);
    return res.json({
      ...cachedResult,
      cached: true,
      cacheStats: searchCache.getStats()
    });
  }

  // Optimized shorter prompt for faster processing
  const prompt = `Search query: "${query}"\n\nProvide a concise, factual response with key information and important details. Be direct and informative.`;

  const payload = {
    model: 'gemini-2.5-flash',
    messages: [{ role: 'user', content: prompt }],
    stream: false,
    temperature: 0.1, // Even lower for faster, more focused responses
  };

  try {
    const startTime = Date.now();
    console.log(`[MCP Search Engine] Sending request to Gemini Wrapper at ${GEMINI_WRAPPER_ENDPOINT}`);
    const headers = {
      'Content-Type': 'application/json',
      'Connection': 'keep-alive'
    };

    // Attach Authorization header if API_KEY is available
    const apiKey = process.env.API_KEY || process.env.USER_API_KEY || process.env.ADMIN_API_KEY || process.env.GUEST_API_KEY;
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
      console.log('[MCP Search Engine] Using Authorization header from environment');
    } else {
      console.warn('[MCP Search Engine] No API key found in environment; request may be rejected by wrapper');
    }

    const wrapperResponse = await axiosInstance.post(GEMINI_WRAPPER_ENDPOINT, payload, { headers });
    const endTime = Date.now();
    const responseTimeInMs = endTime - startTime;

    // Extract the content from the response (following OpenAI's structure)
    const summary = wrapperResponse.data?.choices?.[0]?.message?.content;

    if (!summary) {
      throw new Error('No content returned from the model.');
    }

    console.log(`[MCP Search Engine] Successfully received summary from wrapper in ${responseTimeInMs}ms.`);

    // Prepare response
    const response = {
      query: query,
      status: 'success',
      responseTimeInMs: responseTimeInMs,
      cached: false,
      results: [
        {
          source: 'Gemini-CLI-Wrapper (gemini-2.5-flash)',
          relevance_score: 0.95,
          summary: summary.trim(),
        },
      ],
      cacheStats: searchCache.getStats()
    };

    // Cache the response for future use
    searchCache.set(query, response);

    // Return the structured JSON response
    res.json(response);

  } catch (error) {
    console.error('[MCP Search Engine] Error calling Gemini Wrapper:', error.response ? JSON.stringify(error.response.data, null, 2) : error.message);
    res.status(500).json({
      query: query,
      status: 'error',
      message: 'Failed to communicate with the Gemini Wrapper API.',
      error_details: error.response ? error.response.data : { message: error.message },
    });
  }
});

app.listen(port, () => {
  console.log(`🚀 MCP Search Engine is running on http://localhost:${port}`);
  console.log('Ready to receive search queries at /search');
});