#!/bin/bash

# Fix Gemini CLI issues script

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}=== Fixing Gemini CLI Issues ===${NC}"

# 1. Disable MCP client connections to avoid pandas-data errors
echo -e "\n${YELLOW}1. Disabling MCP client connections...${NC}"

# Create a temporary Gemini CLI config to disable MCP
GEMINI_CONFIG_DIR="$HOME/.config/gemini-cli"
mkdir -p "$GEMINI_CONFIG_DIR"

# Create config to disable MCP servers
cat > "$GEMINI_CONFIG_DIR/config.json" << 'EOF'
{
  "mcpServers": {},
  "disableMcpServers": true,
  "defaultModel": "gemini-2.5-flash"
}
EOF

echo -e "${GREEN}✅ Created Gemini CLI config to disable MCP servers${NC}"

# 2. Set environment variables to disable MCP
echo -e "\n${YELLOW}2. Setting environment variables...${NC}"

# Add to .env if not present
if ! grep -q "GEMINI_DISABLE_MCP" .env; then
    echo "" >> .env
    echo "# Disable Gemini CLI MCP connections to avoid pandas-data errors" >> .env
    echo "GEMINI_DISABLE_MCP=true" >> .env
    echo "GEMINI_NO_MCP_SERVERS=true" >> .env
    echo -e "${GREEN}✅ Added MCP disable flags to .env${NC}"
else
    echo -e "${YELLOW}⚠️  MCP disable flags already exist in .env${NC}"
fi

# 3. Update ecosystem config
echo -e "\n${YELLOW}3. Updating PM2 ecosystem config...${NC}"

# Backup ecosystem config
cp ecosystem.config.cjs ecosystem.config.cjs.backup

# Add MCP disable environment variables to ecosystem config
cat > ecosystem.config.cjs << 'EOF'
module.exports = {
  apps: [{
    name: 'gemini-cli-wrapper',
    script: 'src/index.js',
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 8010,
      ENABLE_REAL_THINKING: 'false',
      GEMINI_CLI_SUPPORTS_REASONING: 'false',
      ENABLE_FAKE_THINKING: 'false',
      STREAM_THINKING_AS_CONTENT: 'false',
      DEFAULT_THINKING_BUDGET: '0',
      ENABLE_VISION: 'true',
      TOKEN_CACHE_ENABLED: 'true',
      GEMINI_USE_OAUTH: 'true',
      GEMINI_DISABLE_MCP: 'true',
      GEMINI_NO_MCP_SERVERS: 'true',
      LOG_LEVEL: 'info'
    },
    error_file: 'logs/err.log',
    out_file: 'logs/out.log',
    log_file: 'logs/combined.log',
    time: true,
    autorestart: true,
    restart_delay: 10000,
    max_restarts: 50,
    exp_backoff_restart_delay: 2000,
    min_uptime: '30s',
    kill_timeout: 5000,
    node_args: '--experimental-modules --es-module-specifier-resolution=node',
    interpreter: 'node',
    interpreter_args: '--experimental-modules'
  }, {
    name: 'mcp-search-engine',
    script: 'mcp_search_engine/index.js',
    cwd: '/home/<USER>/Gemini CLI Wrapper',
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    max_memory_restart: '500M',
    env: {
      NODE_ENV: 'production',
      PORT: 7860
    },
    error_file: 'logs/mcp-search-err.log',
    out_file: 'logs/mcp-search-out.log',
    log_file: 'logs/mcp-search-combined.log',
    time: true,
    autorestart: true,
    restart_delay: 5000,
    max_restarts: 10,
    exp_backoff_restart_delay: 1000,
    min_uptime: '10s',
    kill_timeout: 5000,
    interpreter: 'node'
  }]
};
EOF

echo -e "${GREEN}✅ Updated ecosystem config with MCP disable flags${NC}"

# 4. Test Gemini CLI without MCP
echo -e "\n${YELLOW}4. Testing Gemini CLI without MCP...${NC}"

# Test if Gemini CLI works without MCP
GEMINI_TEST=$(timeout 10s npx @google/gemini-cli --help 2>&1 | head -1)
if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ Gemini CLI is accessible${NC}"
else
    echo -e "${RED}❌ Gemini CLI test failed${NC}"
fi

# 5. Restart services
echo -e "\n${YELLOW}5. Restarting services...${NC}"

pm2 restart all --update-env

echo -e "${GREEN}✅ Services restarted with new configuration${NC}"

# 6. Wait and test
echo -e "\n${YELLOW}6. Testing after restart...${NC}"
sleep 5

# Test health
HEALTH_TEST=$(curl -s http://localhost:8010/health --max-time 5)
if [[ "$HEALTH_TEST" == *"healthy"* ]]; then
    echo -e "${GREEN}✅ Gemini Wrapper: Healthy${NC}"
else
    echo -e "${RED}❌ Gemini Wrapper: Not responding${NC}"
fi

# Test MCP Search
MCP_TEST=$(curl -s http://localhost:7860/cache/stats --max-time 5)
if [[ "$MCP_TEST" == *"hits"* ]]; then
    echo -e "${GREEN}✅ MCP Search Engine: Healthy${NC}"
else
    echo -e "${RED}❌ MCP Search Engine: Not responding${NC}"
fi

# 7. Test fact check tool
echo -e "\n${YELLOW}7. Testing fact check tool...${NC}"

FACT_CHECK_START=$(date +%s%3N)
FACT_CHECK_RESPONSE=$(curl -s -X POST http://localhost:8010/mcp/tools/execute \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer test-key" \
    -d '{"tool_name": "fact_check", "args": {"statement": "2+2=4", "detailed": false}}' \
    --max-time 20)
FACT_CHECK_END=$(date +%s%3N)
FACT_CHECK_TIME=$((FACT_CHECK_END - FACT_CHECK_START))

if [[ "$FACT_CHECK_RESPONSE" == *"success"* ]]; then
    ANALYSIS=$(echo "$FACT_CHECK_RESPONSE" | jq -r '.result.analysis' 2>/dev/null)
    if [[ -n "$ANALYSIS" && "$ANALYSIS" != "null" && "$ANALYSIS" != "" ]]; then
        echo -e "${GREEN}✅ Fact Check: Working (${FACT_CHECK_TIME}ms)${NC}"
        echo -e "${BLUE}   Analysis: ${ANALYSIS:0:100}...${NC}"
    else
        echo -e "${YELLOW}⚠️  Fact Check: Success but empty analysis (${FACT_CHECK_TIME}ms)${NC}"
    fi
else
    echo -e "${RED}❌ Fact Check: Failed (${FACT_CHECK_TIME}ms)${NC}"
fi

# Summary
echo -e "\n${BLUE}=== Fix Summary ===${NC}"
echo -e "${GREEN}✅ Applied fixes:${NC}"
echo -e "   • Disabled MCP client connections"
echo -e "   • Fixed circular dependency in toolRegistry"
echo -e "   • Updated environment variables"
echo -e "   • Restarted services with new config"

echo -e "\n${YELLOW}📊 Next steps:${NC}"
echo -e "   • Monitor logs: pm2 logs gemini-cli-wrapper"
echo -e "   • Test tools: ./quick_test.sh"
echo -e "   • Check performance: ./test_all_endpoints.sh"

echo -e "\n${GREEN}🎉 Gemini CLI issues fix completed!${NC}"
