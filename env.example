# Server Configuration
PORT=8010
NODE_ENV=development

# API Security
API_KEY=your-secret-api-key-here

# Gemini Authentication (choose one method)
# Method 1: API Key Authentication
GEMINI_API_KEY=your-gemini-api-key-from-google-ai-studio

# Method 2: OAuth Authentication
GEMINI_USE_OAUTH=false

# === CLAUDE INTEGRATION ===

# Claude Authentication - Sử dụng API key từ bên thứ 3
ANTHROPIC_AUTH_TOKEN=your-claude-api-key-here

# Claude Base URL - URL của service bên thứ 3 cung cấp Claude API
ANTHROPIC_BASE_URL=https://your-claude-provider-url.com

# === QWEN (OPENAI-COMPATIBLE) INTEGRATION ===

# Qwen Authentication - OpenAI-compatible providers (DashScope, ModelScope, OpenRouter)
# Choose one method below. If QWEN_USE_OAUTH=true, provider will prefer OAuth token.
QWEN_USE_OAUTH=false
QWEN_OAUTH_TOKEN=
QWEN_API_KEY=

# Base URL examples:
# - DashScope (Mainland China): https://dashscope.aliyuncs.com/compatible-mode/v1
# - DashScope (International): https://dashscope-intl.aliyuncs.com/compatible-mode/v1
# - ModelScope: https://api-inference.modelscope.cn/v1
# - OpenRouter: https://openrouter.ai/api/v1
QWEN_API_BASE_URL=https://openrouter.ai/api/v1

# Optional headers for some providers (e.g., OpenRouter)
QWEN_HTTP_REFERER=
QWEN_HTTP_TITLE=Gemini CLI Wrapper

# Logging
LOG_LEVEL=info

# Timeouts (in milliseconds) - Optimized for better performance
REQUEST_TIMEOUT=20000
GEMINI_CLI_TIMEOUT=30000

# MCP Search Engine Performance Settings
MCP_SEARCH_CACHE_TTL=300000
MCP_SEARCH_CACHE_SIZE=100
MCP_SEARCH_TIMEOUT=25000

# === TÍNH NĂNG MỚI ===

# Real Thinking (Native Gemini Reasoning) - BẬT để dùng reasoning thật của Gemini
# Lưu ý: yêu cầu Node.js v20+ và Gemini CLI mới
ENABLE_REAL_THINKING=true

# Thinking Models - Hiển thị quá trình suy nghĩ của AI (TẮT để tiết kiệm token)
ENABLE_FAKE_THINKING=false

# Stream thinking as content với <thinking> tags (DeepSeek R1 style)
STREAM_THINKING_AS_CONTENT=false

# Default thinking budget (-1 = để Gemini tự phân bổ; 0 = tắt reasoning)
DEFAULT_THINKING_BUDGET=-1

# Flag compatibility: set to true ONLY if your installed @google/gemini-cli supports
#   --thinking-budget and --include-reasoning flags. Otherwise leave false to avoid errors.
GEMINI_CLI_SUPPORTS_REASONING=false

# Google OAuth2 credentials (tùy chọn, cho token caching nâng cao)
GOOGLE_CLIENT_ID=77185425430.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Vision Support - Tự động bật cho các model hỗ trợ
ENABLE_VISION=true

# Token cache settings
TOKEN_CACHE_ENABLED=true
TOKEN_REFRESH_BUFFER=300000

# === SECURITY & PERMISSIONS ===

# CẢNH BÁO: Chỉ bật trong môi trường an toàn và tin cậy!
# Unrestricted Mode - Cho phép AI truy cập toàn bộ hệ thống
# - Đọc/ghi file ở bất kỳ đâu trên server
# - Thực thi bất kỳ lệnh nào
# - Truy cập các thư mục hệ thống
GEMINI_UNRESTRICTED_MODE=false

# Khi GEMINI_UNRESTRICTED_MODE=true:
# ⚠️  AI có thể đọc/ghi file: /etc/passwd, /home/<USER>/.ssh/id_rsa, etc.
# ⚠️  AI có thể chạy lệnh: rm -rf /, sudo, curl, wget, etc.
# ⚠️  AI có thể truy cập database, API keys, secrets
# ⚠️  Chỉ sử dụng trong sandbox hoặc container isolated

# === OPEN WEBUI INTEGRATION ===

# Open WebUI Base URL for integration tools
OPEN_WEBUI_BASE_URL=http://localhost:3000

# Enable Open WebUI tools integration
ENABLE_OPEN_WEBUI_TOOLS=true

# Open WebUI API authentication (if required)
OPEN_WEBUI_API_KEY=

# Browser automation settings for Open WebUI interaction
ENABLE_BROWSER_AUTOMATION=false
PUPPETEER_HEADLESS=true