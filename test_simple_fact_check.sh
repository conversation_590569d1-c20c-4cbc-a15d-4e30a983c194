#!/bin/bash

# Simple test for fact_check tool

BASE_URL="http://localhost:8010"
API_KEY="test-key"

echo "🧪 Testing fact_check tool with Gemini provider..."

curl -X POST "$BASE_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "tool_name": "fact_check",
    "args": {
      "statement": "Việt Nam có 63 tỉnh thành",
      "detailed": false
    }
  }' | jq '.'

echo -e "\n✅ Test completed!"
