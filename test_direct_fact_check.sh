#!/bin/bash

# Test script for direct tool execution

BASE_URL="http://localhost:8010"
API_KEY="test-key"

echo "🧪 Testing direct tool execution..."

# Test 1: Direct fact_check tool execution
echo -e "\n📋 Test 1: Direct fact_check tool execution"

curl -X POST "$BASE_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "tool_name": "fact_check",
    "args": {
      "statement": "Việt Nam có 63 tỉnh thành",
      "detailed": true
    }
  }' | jq '.'

echo -e "\n\n📋 Test 2: Direct research_topic tool execution"

curl -X POST "$BASE_URL/mcp/tools/execute" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_KEY" \
  -d '{
    "tool_name": "research_topic",
    "args": {
      "topic": "Tình hình kinh tế Việt Nam 2024",
      "depth": "basic"
    }
  }' | jq '.'

echo -e "\n\n📋 Test 3: List available tools"

curl -X GET "$BASE_URL/mcp/tools" \
  -H "Authorization: Bearer $API_KEY" | jq '.tools[] | select(.name == "fact_check" or .name == "research_topic")'

echo -e "\n✅ Direct tool test completed!"
