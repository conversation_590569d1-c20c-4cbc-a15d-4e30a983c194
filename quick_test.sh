#!/bin/bash

# Quick performance test script

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}=== Quick Performance Test ===${NC}"

# Test 1: Health check
echo -e "\n${YELLOW}1. Testing health endpoints...${NC}"
HEALTH_START=$(date +%s%3N)
HEALTH_RESPONSE=$(curl -s http://localhost:8010/health --max-time 5)
HEALTH_END=$(date +%s%3N)
HEALTH_TIME=$((HEALTH_END - HEALTH_START))

if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
    echo -e "${GREEN}✅ Gemini Wrapper: ${HEALTH_TIME}ms${NC}"
else
    echo -e "${RED}❌ Gemini Wrapper: Failed${NC}"
fi

# Test 2: Cache stats
echo -e "\n${YELLOW}2. Testing MCP Search Engine cache...${NC}"
CACHE_START=$(date +%s%3N)
CACHE_RESPONSE=$(curl -s http://localhost:7860/cache/stats --max-time 5)
CACHE_END=$(date +%s%3N)
CACHE_TIME=$((CACHE_END - CACHE_START))

if [[ "$CACHE_RESPONSE" == *"hits"* ]]; then
    echo -e "${GREEN}✅ MCP Cache: ${CACHE_TIME}ms${NC}"
    echo -e "${BLUE}📊 Cache Stats: $CACHE_RESPONSE${NC}"
else
    echo -e "${RED}❌ MCP Cache: Failed${NC}"
fi

# Test 3: Simple search (with timeout)
echo -e "\n${YELLOW}3. Testing simple search (30s timeout)...${NC}"
SEARCH_START=$(date +%s%3N)
SEARCH_RESPONSE=$(curl -s -X POST http://localhost:7860/search \
    -H "Content-Type: application/json" \
    -d '{"query": "What is 2+2?"}' \
    --max-time 30)
SEARCH_END=$(date +%s%3N)
SEARCH_TIME=$((SEARCH_END - SEARCH_START))

if [[ "$SEARCH_RESPONSE" == *"success"* ]]; then
    echo -e "${GREEN}✅ Search: ${SEARCH_TIME}ms${NC}"
    
    # Check if cached
    if [[ "$SEARCH_RESPONSE" == *'"cached":true'* ]]; then
        echo -e "${BLUE}📋 Result: Cache HIT${NC}"
    else
        echo -e "${YELLOW}📋 Result: Cache MISS${NC}"
    fi
    
    # Extract server response time
    SERVER_TIME=$(echo "$SEARCH_RESPONSE" | grep -o '"responseTimeInMs":[^,]*' | cut -d':' -f2)
    if [[ -n "$SERVER_TIME" ]]; then
        echo -e "${BLUE}⏱️  Server Time: ${SERVER_TIME}ms${NC}"
    fi
else
    echo -e "${RED}❌ Search: Failed (${SEARCH_TIME}ms)${NC}"
    if [[ $SEARCH_TIME -ge 30000 ]]; then
        echo -e "${RED}   Reason: Timeout${NC}"
    fi
fi

# Test 4: Repeat search for cache test
echo -e "\n${YELLOW}4. Testing cache hit (repeat search)...${NC}"
CACHE_TEST_START=$(date +%s%3N)
CACHE_TEST_RESPONSE=$(curl -s -X POST http://localhost:7860/search \
    -H "Content-Type: application/json" \
    -d '{"query": "What is 2+2?"}' \
    --max-time 10)
CACHE_TEST_END=$(date +%s%3N)
CACHE_TEST_TIME=$((CACHE_TEST_END - CACHE_TEST_START))

if [[ "$CACHE_TEST_RESPONSE" == *"success"* ]]; then
    if [[ "$CACHE_TEST_RESPONSE" == *'"cached":true'* ]]; then
        echo -e "${GREEN}✅ Cache Hit: ${CACHE_TEST_TIME}ms${NC}"
    else
        echo -e "${YELLOW}⚠️  Cache Miss: ${CACHE_TEST_TIME}ms${NC}"
    fi
else
    echo -e "${RED}❌ Cache Test: Failed${NC}"
fi

# Summary
echo -e "\n${BLUE}=== Performance Summary ===${NC}"
echo -e "Health Check: ${HEALTH_TIME}ms"
echo -e "Cache Stats: ${CACHE_TIME}ms"
echo -e "Search Query: ${SEARCH_TIME}ms"
echo -e "Cache Test: ${CACHE_TEST_TIME}ms"

# Performance rating
if [[ $SEARCH_TIME -lt 10000 ]]; then
    echo -e "\n${GREEN}🎉 Performance: Excellent (<10s)${NC}"
elif [[ $SEARCH_TIME -lt 20000 ]]; then
    echo -e "\n${YELLOW}👍 Performance: Good (10-20s)${NC}"
elif [[ $SEARCH_TIME -lt 40000 ]]; then
    echo -e "\n${YELLOW}⚠️  Performance: Fair (20-40s)${NC}"
else
    echo -e "\n${RED}❌ Performance: Poor (>40s)${NC}"
fi

# Show PM2 status
echo -e "\n${BLUE}📊 PM2 Status:${NC}"
pm2 list
