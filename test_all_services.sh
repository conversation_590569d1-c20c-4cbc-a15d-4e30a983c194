#!/bin/bash

# Script test tất cả services
# Kiểm tra Gemini CLI Wrapper và MCP Search Engine

# Màu sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Test All Services ===${NC}"

# Kiểm tra PM2 status
echo -e "\n${BLUE}1. PM2 Status:${NC}"
pm2 status

# Test Gemini CLI Wrapper
echo -e "\n${BLUE}2. Testing Gemini CLI Wrapper (port 8010):${NC}"
GEMINI_RESPONSE=$(curl -s -w "%{http_code}" http://localhost:8010/health)
GEMINI_HTTP_CODE="${GEMINI_RESPONSE: -3}"
GEMINI_BODY="${GEMINI_RESPONSE%???}"

if [[ "$GEMINI_HTTP_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ Gemini CLI Wrapper: OK${NC}"
    echo -e "   Response: $GEMINI_BODY"
else
    echo -e "${RED}❌ Gemini CLI Wrapper: FAILED (HTTP $GEMINI_HTTP_CODE)${NC}"
fi

# Test MCP Search Engine
echo -e "\n${BLUE}3. Testing MCP Search Engine (port 7860):${NC}"
echo -e "${YELLOW}   Sending test query...${NC}"

MCP_RESPONSE=$(curl -s -X POST http://localhost:7860/search \
    -H "Content-Type: application/json" \
    -d '{"query": "What is JavaScript?"}' \
    -w "%{http_code}" \
    --max-time 60)

MCP_HTTP_CODE="${MCP_RESPONSE: -3}"
MCP_BODY="${MCP_RESPONSE%???}"

if [[ "$MCP_HTTP_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ MCP Search Engine: OK${NC}"
    # Parse JSON response để lấy status
    MCP_STATUS=$(echo "$MCP_BODY" | jq -r '.status' 2>/dev/null || echo "unknown")
    echo -e "   Status: $MCP_STATUS"
    if [[ "$MCP_STATUS" == "success" ]]; then
        echo -e "${GREEN}   ✅ Search query successful${NC}"
    else
        echo -e "${YELLOW}   ⚠️  Search query status: $MCP_STATUS${NC}"
    fi
else
    echo -e "${RED}❌ MCP Search Engine: FAILED (HTTP $MCP_HTTP_CODE)${NC}"
    echo -e "   Response: $MCP_BODY"
fi

# Kiểm tra ports
echo -e "\n${BLUE}4. Port Status:${NC}"
netstat -tlnp 2>/dev/null | grep -E ":(7860|8010|8000)" | while read line; do
    if echo "$line" | grep -q ":7860"; then
        echo -e "${GREEN}✅ Port 7860 (MCP Search Engine): LISTENING${NC}"
    elif echo "$line" | grep -q ":8010"; then
        echo -e "${GREEN}✅ Port 8010 (Gemini CLI Wrapper): LISTENING${NC}"
    elif echo "$line" | grep -q ":8000"; then
        echo -e "${GREEN}✅ Port 8000 (MCPO): LISTENING${NC}"
    fi
done

# Hiển thị thông tin truy cập
SERVER_IP=$(hostname -I | awk '{print $1}')
echo -e "\n${BLUE}5. Access Information:${NC}"
echo -e "${GREEN}Gemini CLI Wrapper:${NC}"
echo -e "   Local: http://localhost:8010"
echo -e "   External: http://$SERVER_IP:8010"
echo -e "${GREEN}MCP Search Engine:${NC}"
echo -e "   Local: http://localhost:7860"
echo -e "   External: http://$SERVER_IP:7860"

echo -e "\n${GREEN}Test completed!${NC}"
