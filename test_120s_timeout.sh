#!/bin/bash

# Test performance with 120s timeout

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}=== Testing with 120s Timeout ===${NC}"

# 1. Restart services to apply new timeout settings
echo -e "\n${YELLOW}1. Restarting services with 120s timeout...${NC}"
pm2 restart all --update-env
sleep 5

# 2. Test basic health
echo -e "\n${YELLOW}2. Testing basic health...${NC}"
HEALTH_START=$(date +%s%3N)
HEALTH_RESPONSE=$(curl -s http://localhost:8010/health --max-time 5)
HEALTH_END=$(date +%s%3N)
HEALTH_TIME=$((HEALTH_END - HEALTH_START))

if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
    echo -e "${GREEN}✅ Health Check: ${HEALTH_TIME}ms${NC}"
else
    echo -e "${RED}❌ Health Check: Failed${NC}"
fi

# 3. Test MCP Search Engine with 120s timeout
echo -e "\n${YELLOW}3. Testing MCP Search Engine (120s timeout)...${NC}"

# Test 1: Simple query
echo -e "\n${BLUE}Test 1: Simple math query${NC}"
MATH_START=$(date +%s%3N)
MATH_RESPONSE=$(curl -s -X POST http://localhost:7860/search \
    -H "Content-Type: application/json" \
    -d '{"query": "What is 10+10?"}' \
    --max-time 120)
MATH_END=$(date +%s%3N)
MATH_TIME=$((MATH_END - MATH_START))

if [[ "$MATH_RESPONSE" == *"success"* ]]; then
    echo -e "${GREEN}✅ Math Query: ${MATH_TIME}ms${NC}"
    SERVER_TIME=$(echo "$MATH_RESPONSE" | grep -o '"responseTimeInMs":[^,]*' | cut -d':' -f2)
    if [[ -n "$SERVER_TIME" ]]; then
        echo -e "${BLUE}   Server Time: ${SERVER_TIME}ms${NC}"
    fi
    
    # Show summary
    SUMMARY=$(echo "$MATH_RESPONSE" | jq -r '.results[0].summary' 2>/dev/null)
    if [[ -n "$SUMMARY" && "$SUMMARY" != "null" ]]; then
        echo -e "${BLUE}   Summary: ${SUMMARY:0:100}...${NC}"
    fi
else
    echo -e "${RED}❌ Math Query: Failed (${MATH_TIME}ms)${NC}"
    echo -e "${RED}   Response: ${MATH_RESPONSE:0:100}...${NC}"
fi

# Test 2: Vietnam UAV query
echo -e "\n${BLUE}Test 2: Vietnam UAV query${NC}"
UAV_START=$(date +%s%3N)
UAV_RESPONSE=$(curl -s -X POST http://localhost:7860/search \
    -H "Content-Type: application/json" \
    -d '{"query": "việt nam sản xuất uav chiến đấu"}' \
    --max-time 120)
UAV_END=$(date +%s%3N)
UAV_TIME=$((UAV_END - UAV_START))

if [[ "$UAV_RESPONSE" == *"success"* ]]; then
    echo -e "${GREEN}✅ UAV Query: ${UAV_TIME}ms${NC}"
    SERVER_TIME=$(echo "$UAV_RESPONSE" | grep -o '"responseTimeInMs":[^,]*' | cut -d':' -f2)
    if [[ -n "$SERVER_TIME" ]]; then
        echo -e "${BLUE}   Server Time: ${SERVER_TIME}ms${NC}"
    fi
    
    # Show summary
    SUMMARY=$(echo "$UAV_RESPONSE" | jq -r '.results[0].summary' 2>/dev/null)
    if [[ -n "$SUMMARY" && "$SUMMARY" != "null" ]]; then
        echo -e "${BLUE}   Summary: ${SUMMARY:0:150}...${NC}"
    fi
else
    echo -e "${RED}❌ UAV Query: Failed (${UAV_TIME}ms)${NC}"
    echo -e "${RED}   Response: ${UAV_RESPONSE:0:100}...${NC}"
fi

# Test 3: Cache hit test
echo -e "\n${BLUE}Test 3: Cache hit test (repeat math query)${NC}"
CACHE_START=$(date +%s%3N)
CACHE_RESPONSE=$(curl -s -X POST http://localhost:7860/search \
    -H "Content-Type: application/json" \
    -d '{"query": "What is 10+10?"}' \
    --max-time 10)
CACHE_END=$(date +%s%3N)
CACHE_TIME=$((CACHE_END - CACHE_START))

if [[ "$CACHE_RESPONSE" == *"success"* ]]; then
    if [[ "$CACHE_RESPONSE" == *'"cached":true'* ]]; then
        echo -e "${GREEN}✅ Cache Hit: ${CACHE_TIME}ms${NC}"
    else
        echo -e "${YELLOW}⚠️  Cache Miss: ${CACHE_TIME}ms${NC}"
    fi
else
    echo -e "${RED}❌ Cache Test: Failed${NC}"
fi

# 4. Test fact_check with 120s timeout
echo -e "\n${YELLOW}4. Testing fact_check (120s timeout)...${NC}"

FACT_START=$(date +%s%3N)
FACT_RESPONSE=$(curl -s -X POST http://localhost:8010/mcp/tools/execute \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer test-key" \
    -d '{"tool_name": "fact_check", "args": {"statement": "Vietnam has 63 provinces and cities", "detailed": true}}' \
    --max-time 120)
FACT_END=$(date +%s%3N)
FACT_TIME=$((FACT_END - FACT_START))

if [[ "$FACT_RESPONSE" == *"success"* ]]; then
    ANALYSIS=$(echo "$FACT_RESPONSE" | jq -r '.result.analysis' 2>/dev/null)
    if [[ -n "$ANALYSIS" && "$ANALYSIS" != "null" && "$ANALYSIS" != "" ]]; then
        echo -e "${GREEN}✅ Fact Check: Working (${FACT_TIME}ms)${NC}"
        echo -e "${BLUE}   Analysis: ${ANALYSIS:0:100}...${NC}"
    else
        echo -e "${YELLOW}⚠️  Fact Check: Success but empty analysis (${FACT_TIME}ms)${NC}"
    fi
else
    echo -e "${RED}❌ Fact Check: Failed/Timeout (${FACT_TIME}ms)${NC}"
fi

# 5. Test web_search with 120s timeout
echo -e "\n${YELLOW}5. Testing web_search (120s timeout)...${NC}"

WEB_START=$(date +%s%3N)
WEB_RESPONSE=$(curl -s -X POST http://localhost:8010/mcp/tools/execute \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer test-key" \
    -d '{"tool_name": "web_search", "args": {"query": "Vietnam UAV drone", "max_results": 3}}' \
    --max-time 120)
WEB_END=$(date +%s%3N)
WEB_TIME=$((WEB_END - WEB_START))

if [[ "$WEB_RESPONSE" == *"success"* ]]; then
    echo -e "${GREEN}✅ Web Search: Working (${WEB_TIME}ms)${NC}"
    # Try to extract results
    RESULTS=$(echo "$WEB_RESPONSE" | jq -r '.result.results' 2>/dev/null)
    if [[ -n "$RESULTS" && "$RESULTS" != "null" ]]; then
        echo -e "${BLUE}   Results found${NC}"
    fi
else
    echo -e "${RED}❌ Web Search: Failed/Timeout (${WEB_TIME}ms)${NC}"
fi

# 6. Cache statistics
echo -e "\n${YELLOW}6. Cache statistics...${NC}"
CACHE_STATS=$(curl -s http://localhost:7860/cache/stats --max-time 5)
if [[ "$CACHE_STATS" == *"hits"* ]]; then
    echo -e "${GREEN}✅ Cache Stats:${NC}"
    echo "$CACHE_STATS" | jq '.' 2>/dev/null || echo "$CACHE_STATS"
else
    echo -e "${RED}❌ Could not retrieve cache stats${NC}"
fi

# 7. Performance summary
echo -e "\n${BLUE}=== Performance Summary with 120s Timeout ===${NC}"
echo -e "Health Check: ${HEALTH_TIME}ms"
echo -e "Math Query: ${MATH_TIME}ms"
echo -e "UAV Query: ${UAV_TIME}ms"
echo -e "Cache Hit: ${CACHE_TIME}ms"
echo -e "Fact Check: ${FACT_TIME}ms"
echo -e "Web Search: ${WEB_TIME}ms"

# Performance assessment
echo -e "\n${BLUE}=== Performance Assessment ===${NC}"

# Overall assessment
TOTAL_SUCCESSFUL=0
TOTAL_TESTS=6

if [[ $HEALTH_TIME -lt 1000 ]]; then
    echo -e "${GREEN}✅ Health Check: Excellent${NC}"
    TOTAL_SUCCESSFUL=$((TOTAL_SUCCESSFUL + 1))
fi

if [[ "$MATH_RESPONSE" == *"success"* ]]; then
    if [[ $MATH_TIME -lt 60000 ]]; then
        echo -e "${GREEN}✅ Math Query: Good (<60s)${NC}"
    else
        echo -e "${YELLOW}⚠️  Math Query: Slow (>60s)${NC}"
    fi
    TOTAL_SUCCESSFUL=$((TOTAL_SUCCESSFUL + 1))
fi

if [[ "$UAV_RESPONSE" == *"success"* ]]; then
    if [[ $UAV_TIME -lt 90000 ]]; then
        echo -e "${GREEN}✅ UAV Query: Good (<90s)${NC}"
    else
        echo -e "${YELLOW}⚠️  UAV Query: Slow (>90s)${NC}"
    fi
    TOTAL_SUCCESSFUL=$((TOTAL_SUCCESSFUL + 1))
fi

if [[ "$CACHE_RESPONSE" == *"success"* && "$CACHE_RESPONSE" == *'"cached":true'* ]]; then
    echo -e "${GREEN}✅ Cache: Working perfectly${NC}"
    TOTAL_SUCCESSFUL=$((TOTAL_SUCCESSFUL + 1))
fi

if [[ "$FACT_RESPONSE" == *"success"* ]]; then
    echo -e "${GREEN}✅ Fact Check: Responding${NC}"
    TOTAL_SUCCESSFUL=$((TOTAL_SUCCESSFUL + 1))
fi

if [[ "$WEB_RESPONSE" == *"success"* ]]; then
    echo -e "${GREEN}✅ Web Search: Working${NC}"
    TOTAL_SUCCESSFUL=$((TOTAL_SUCCESSFUL + 1))
fi

echo -e "\n${BLUE}📊 Success Rate: ${TOTAL_SUCCESSFUL}/${TOTAL_TESTS} ($(( TOTAL_SUCCESSFUL * 100 / TOTAL_TESTS ))%)${NC}"

echo -e "\n${GREEN}🔧 Applied Changes:${NC}"
echo -e "   • Timeout increased to 120s for all services"
echo -e "   • Removed pandas-data MCP server"
echo -e "   • Response caching enabled"
echo -e "   • Connection keep-alive optimized"

echo -e "\n${GREEN}✅ 120s timeout test completed!${NC}"
