#!/bin/bash

# Script thiết lập PM2 auto-start với systemd
# Đảm bảo PM2 và các services tự khởi động khi server restart

# Màu sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Setup PM2 Auto-Start với Systemd ===${NC}"

# Kiểm tra quyền sudo
if [[ $EUID -eq 0 ]]; then
   echo -e "${RED}❌ Không chạy script này với sudo!${NC}"
   echo -e "${YELLOW}Hãy chạy: bash setup_pm2_autostart.sh${NC}"
   exit 1
fi

# Chuyển đến thư mục dự án
cd "/home/<USER>/Gemini CLI Wrapper"

# Kiểm tra PM2 đã được cài đặt chưa
if ! command -v pm2 &> /dev/null; then
    echo -e "${RED}❌ PM2 chưa được cài đặt!${NC}"
    echo -e "${YELLOW}Đang cài đặt PM2...${NC}"
    npm install -g pm2
fi

# Dừng tất cả processes hiện tại
echo -e "${YELLOW}Dừng tất cả PM2 processes...${NC}"
pm2 delete all 2>/dev/null || true

# Khởi động từ ecosystem config
echo -e "${YELLOW}Khởi động services từ ecosystem config...${NC}"
pm2 start ecosystem.config.cjs

# Lưu cấu hình PM2
echo -e "${YELLOW}Lưu cấu hình PM2...${NC}"
pm2 save

# Tạo systemd service cho PM2
echo -e "${YELLOW}Tạo systemd service cho PM2...${NC}"
pm2 startup systemd -u ubuntu --hp /home/<USER>

echo -e "\n${GREEN}✅ Setup hoàn thành!${NC}"
echo -e "\n${BLUE}Để hoàn tất setup, hãy chạy lệnh sau với sudo:${NC}"
echo -e "${YELLOW}sudo env PATH=\$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u ubuntu --hp /home/<USER>"

echo -e "\n${BLUE}Sau đó chạy:${NC}"
echo -e "${YELLOW}pm2 save${NC}"

echo -e "\n${BLUE}Kiểm tra trạng thái:${NC}"
pm2 status

echo -e "\n${GREEN}Services sẽ tự khởi động khi server restart!${NC}"
