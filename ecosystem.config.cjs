module.exports = {
  apps: [{
    name: 'gemini-cli-wrapper',
    script: 'src/index.js',
    watch: false, // Disable watch to prevent unnecessary restarts
    ignore_watch: ['node_modules', 'logs'],
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 8010,
      ENABLE_REAL_THINKING: 'false',
      GEMINI_CLI_SUPPORTS_REASONING: 'false',
      ENABLE_FAKE_THINKING: 'false',
      STREAM_THINKING_AS_CONTENT: 'false',
      DEFAULT_THINKING_BUDGET: '0',
      ENABLE_VISION: 'true',
      TOKEN_CACHE_ENABLED: 'true',
      GEMINI_USE_OAUTH: 'true',
      LOG_LEVEL: 'info'
    },
    error_file: 'logs/err.log',
    out_file: 'logs/out.log',
    log_file: 'logs/combined.log',
    time: true,
    autorestart: true,
    restart_delay: 10000, // Increased delay to handle quota issues
    max_restarts: 50, // Increased max restarts
    exp_backoff_restart_delay: 2000, // Exponential backoff
    min_uptime: '30s', // Minimum uptime before considering stable
    kill_timeout: 5000,
    node_args: '--experimental-modules --es-module-specifier-resolution=node',
    interpreter: 'node',
    interpreter_args: '--experimental-modules'
  }, {
    name: 'mcp-search-engine',
    script: 'mcp_search_engine/index.js',
    cwd: '/home/<USER>/Gemini CLI Wrapper',
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    max_memory_restart: '500M',
    env: {
      NODE_ENV: 'production',
      PORT: 7860
    },
    error_file: 'logs/mcp-search-err.log',
    out_file: 'logs/mcp-search-out.log',
    log_file: 'logs/mcp-search-combined.log',
    time: true,
    autorestart: true,
    restart_delay: 5000,
    max_restarts: 10,
    exp_backoff_restart_delay: 1000,
    min_uptime: '10s',
    kill_timeout: 5000,
    interpreter: 'node'
  }]
};