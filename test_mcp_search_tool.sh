#!/bin/bash

# Script test MCP Search Tool từ MCPO server
# Test tool_search_with_gemini_post từ Open WebUI

# Màu sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Test MCP Search Tool ===${NC}"

# API key cho MCPO server
API_KEY="acca-enhanced-rag-mcp-key-2025"
BASE_URL="http://localhost:8000"

echo -e "\n${BLUE}1. Testing connection to MCPO server...${NC}"
HEALTH_CHECK=$(curl -s "$BASE_URL/docs" -w "%{http_code}" -o /dev/null)
if [[ "$HEALTH_CHECK" == "200" ]]; then
    echo -e "${GREEN}✅ MCPO server is running${NC}"
else
    echo -e "${RED}❌ MCPO server not accessible (HTTP $HEALTH_CHECK)${NC}"
    exit 1
fi

echo -e "\n${BLUE}2. Testing Gemini CLI Wrapper connection...${NC}"
WRAPPER_HEALTH=$(curl -s http://localhost:8010/health -w "%{http_code}" -o /dev/null)
if [[ "$WRAPPER_HEALTH" == "200" ]]; then
    echo -e "${GREEN}✅ Gemini CLI Wrapper is running${NC}"
else
    echo -e "${RED}❌ Gemini CLI Wrapper not accessible (HTTP $WRAPPER_HEALTH)${NC}"
    exit 1
fi

echo -e "\n${BLUE}3. Testing search_with_gemini tool...${NC}"
echo -e "${YELLOW}   Query: 'What is artificial intelligence?'${NC}"

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST "$BASE_URL/gemini_search_engine/search_with_gemini" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $API_KEY" \
    -d '{"query": "What is artificial intelligence?", "temperature": 0.2, "detailed": false}' \
    --max-time 90 \
    -w "%{http_code}")

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

# Extract HTTP code (last 3 characters)
HTTP_CODE="${RESPONSE: -3}"
RESPONSE_BODY="${RESPONSE%???}"

echo -e "\n${BLUE}Results:${NC}"
echo -e "Duration: ${DURATION}s"
echo -e "HTTP Code: $HTTP_CODE"

if [[ "$HTTP_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ Tool executed successfully!${NC}"
    echo -e "\n${BLUE}Response preview:${NC}"
    echo "$RESPONSE_BODY" | head -c 200
    echo "..."
    
    # Try to parse JSON and extract key info
    if command -v jq &> /dev/null; then
        echo -e "\n${BLUE}Parsed response:${NC}"
        echo "$RESPONSE_BODY" | jq -r '.' 2>/dev/null | head -10
    fi
else
    echo -e "${RED}❌ Tool failed (HTTP $HTTP_CODE)${NC}"
    echo -e "\n${BLUE}Error response:${NC}"
    echo "$RESPONSE_BODY"
fi

echo -e "\n${BLUE}4. Testing quick_search tool...${NC}"
echo -e "${YELLOW}   Query: 'Vietnam capital'${NC}"

START_TIME=$(date +%s)
RESPONSE=$(curl -s -X POST "$BASE_URL/gemini_search_engine/quick_search" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $API_KEY" \
    -d '{"query": "Vietnam capital"}' \
    --max-time 60 \
    -w "%{http_code}")

END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

HTTP_CODE="${RESPONSE: -3}"
RESPONSE_BODY="${RESPONSE%???}"

echo -e "\n${BLUE}Results:${NC}"
echo -e "Duration: ${DURATION}s"
echo -e "HTTP Code: $HTTP_CODE"

if [[ "$HTTP_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ Quick search successful!${NC}"
    echo -e "\n${BLUE}Response:${NC}"
    echo "$RESPONSE_BODY" | head -c 300
else
    echo -e "${RED}❌ Quick search failed (HTTP $HTTP_CODE)${NC}"
    echo -e "\n${BLUE}Error response:${NC}"
    echo "$RESPONSE_BODY"
fi

echo -e "\n${GREEN}Test completed!${NC}"

# Summary
echo -e "\n${BLUE}=== Summary ===${NC}"
echo -e "MCPO Server: ${GREEN}✅ Running${NC}"
echo -e "Gemini CLI Wrapper: ${GREEN}✅ Running${NC}"
echo -e "Search Tools: Check results above"

echo -e "\n${YELLOW}💡 Tips:${NC}"
echo -e "- Use detailed=false for faster responses"
echo -e "- Use quick_search for simple queries"
echo -e "- Check Open WebUI integration with these endpoints"
