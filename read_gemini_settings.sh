#!/bin/bash

# <PERSON><PERSON>t to read and modify Gemini CLI settings

echo "=== Current Gemini CLI Settings ==="
cat ~/.gemini/settings.json

echo -e "\n=== Backup current settings ==="
cp ~/.gemini/settings.json ~/.gemini/settings.json.backup.$(date +%Y%m%d_%H%M%S)

echo -e "\n=== Remove pandas-data from MCP servers ==="
# Create new settings without pandas-data
python3 -c "
import json
import sys

try:
    with open('/home/<USER>/.gemini/settings.json', 'r') as f:
        settings = json.load(f)
    
    print('Original settings:')
    print(json.dumps(settings, indent=2))
    
    # Remove pandas-data if it exists
    if 'mcpServers' in settings and 'pandas-data' in settings['mcpServers']:
        del settings['mcpServers']['pandas-data']
        print('\nRemoved pandas-data from mcpServers')
    else:
        print('\npandas-data not found in mcpServers')
    
    # Write back
    with open('/home/<USER>/.gemini/settings.json', 'w') as f:
        json.dump(settings, f, indent=2)
    
    print('\nUpdated settings:')
    print(json.dumps(settings, indent=2))
    
except Exception as e:
    print(f'Error: {e}')
    sys.exit(1)
"

echo -e "\n=== Verify changes ==="
cat ~/.gemini/settings.json
