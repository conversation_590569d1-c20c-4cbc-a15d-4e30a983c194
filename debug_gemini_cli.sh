#!/bin/bash

# Debug Gemini CLI issues

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}=== Debugging Gemini CLI Issues ===${NC}"

# 1. Check environment
echo -e "\n${YELLOW}1. Checking environment...${NC}"
echo "Node.js version: $(node --version)"
echo "NPM version: $(npm --version)"
echo "API Key (first 20 chars): $(echo $GEMINI_API_KEY | head -c 20)"

# 2. Test Gemini CLI installation
echo -e "\n${YELLOW}2. Testing Gemini CLI installation...${NC}"
GEMINI_VERSION=$(npx @google/gemini-cli --version 2>&1)
echo "Gemini CLI version: $GEMINI_VERSION"

# 3. Test simple prompt with different approaches
echo -e "\n${YELLOW}3. Testing simple prompt...${NC}"

# Test 1: Basic prompt
echo -e "\n${BLUE}Test 1: Basic prompt${NC}"
echo "Command: npx @google/gemini-cli -p 'Hello, respond with just OK'"
timeout 30s npx @google/gemini-cli -p "Hello, respond with just OK" 2>&1 | head -20

# Test 2: With model specification
echo -e "\n${BLUE}Test 2: With model specification${NC}"
echo "Command: npx @google/gemini-cli -p 'What is 2+2?' --model gemini-2.5-flash"
timeout 30s npx @google/gemini-cli -p "What is 2+2?" --model gemini-2.5-flash 2>&1 | head -20

# Test 3: With YOLO mode
echo -e "\n${BLUE}Test 3: With YOLO mode${NC}"
echo "Command: npx @google/gemini-cli -p 'What is 2+2?' -y --model gemini-2.5-flash"
timeout 30s npx @google/gemini-cli -p "What is 2+2?" -y --model gemini-2.5-flash 2>&1 | head -20

# 4. Test authentication
echo -e "\n${YELLOW}4. Testing authentication...${NC}"

# Check if authenticated
echo -e "\n${BLUE}Checking authentication status...${NC}"
timeout 10s npx @google/gemini-cli -p "test" --debug 2>&1 | grep -i "auth\|error\|fail" | head -10

# 5. Test with different output handling
echo -e "\n${YELLOW}5. Testing output handling...${NC}"

# Test with output redirection
echo -e "\n${BLUE}Test with output redirection${NC}"
echo "Command: npx @google/gemini-cli -p 'Say OK' 2>/dev/null"
timeout 20s npx @google/gemini-cli -p "Say OK" 2>/dev/null | head -10

# 6. Check for common issues
echo -e "\n${YELLOW}6. Checking for common issues...${NC}"

# Check if there are any error logs
if [ -f ~/.gemini/logs/error.log ]; then
    echo -e "\n${BLUE}Recent Gemini CLI errors:${NC}"
    tail -10 ~/.gemini/logs/error.log
else
    echo "No Gemini CLI error log found"
fi

# Check settings
if [ -f ~/.gemini/settings.json ]; then
    echo -e "\n${BLUE}Gemini CLI settings:${NC}"
    cat ~/.gemini/settings.json | jq '.selectedAuthType' 2>/dev/null || echo "Could not parse settings"
else
    echo "No Gemini CLI settings found"
fi

# 7. Test with minimal command
echo -e "\n${YELLOW}7. Testing minimal command...${NC}"

# Create a simple test file
echo "Test prompt" > /tmp/test_prompt.txt

echo -e "\n${BLUE}Test with file input${NC}"
timeout 15s npx @google/gemini-cli < /tmp/test_prompt.txt 2>&1 | head -10

# Clean up
rm -f /tmp/test_prompt.txt

# 8. Summary
echo -e "\n${BLUE}=== Debug Summary ===${NC}"
echo -e "${YELLOW}If you see only spinner characters (⠙⠹⠸), it means:${NC}"
echo -e "   • Gemini CLI is running but not producing text output"
echo -e "   • Could be authentication issue"
echo -e "   • Could be API quota/rate limiting"
echo -e "   • Could be model availability issue"
echo -e ""
echo -e "${YELLOW}Possible solutions:${NC}"
echo -e "   • Check API key validity"
echo -e "   • Try different model (gemini-1.5-flash)"
echo -e "   • Check quota limits"
echo -e "   • Re-authenticate Gemini CLI"

echo -e "\n${GREEN}✅ Debug completed!${NC}"
